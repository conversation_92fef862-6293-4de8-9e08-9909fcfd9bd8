#!/usr/bin/env python3
"""
测试基于说明和特征的分析，验证不受名称影响
"""

import requests
import json

def analyze_reasoning_focus(analysis_result):
    """分析推理重点是否基于特征而非名称"""
    if not isinstance(analysis_result, dict):
        return {"focus_on_features": False, "focus_on_names": False, "analysis": "无法分析"}
    
    reason = analysis_result.get('reason', '').lower()
    detailed_analysis = analysis_result.get('detailed_analysis', [])
    
    # 检查是否提到特征相关词汇
    feature_keywords = ['特征', '说明', '工艺', '加工', '本质', '属性', '形态', '功能', '类别', '实质']
    name_keywords = ['名称', '叫做', '称为', '命名', '字面']
    
    feature_mentions = sum(1 for keyword in feature_keywords if keyword in reason)
    name_mentions = sum(1 for keyword in name_keywords if keyword in reason)
    
    # 检查详细分析中的推理重点
    detailed_feature_focus = 0
    detailed_name_focus = 0
    
    for item in detailed_analysis:
        match_reason = item.get('match_reason', '').lower()
        detailed_feature_focus += sum(1 for keyword in feature_keywords if keyword in match_reason)
        detailed_name_focus += sum(1 for keyword in name_keywords if keyword in match_reason)
    
    total_feature_focus = feature_mentions + detailed_feature_focus
    total_name_focus = name_mentions + detailed_name_focus
    
    return {
        "focus_on_features": total_feature_focus > 0,
        "focus_on_names": total_name_focus > 0,
        "feature_mentions": total_feature_focus,
        "name_mentions": total_name_focus,
        "analysis": "主要基于特征分析" if total_feature_focus > total_name_focus else "主要基于名称分析" if total_name_focus > total_feature_focus else "特征和名称并重"
    }

def test_feature_based_analysis():
    """测试基于特征的分析"""
    
    test_cases = [
        {
            "name": "名称差异但特征相似 - 西式火腿vs熟制火腿",
            "data": {
                "品名": "西式火腿",
                "原配料": "猪肉、盐、糖、香辛料",
                "存储条件": "冷藏保存",
                "保质期": "60天",
                "净含量": "500g",
                "食用方法": "切片食用",
                "生产加工工艺": "熏烤制作",
                "食品分类号": "02.01.01",
                "食品分类名称": "西式火腿(熏烤、烟熏、蒸煮火腿)",
                "食品原产地": "德国"
            },
            "expected_focus": "特征分析",
            "test_point": "名称不同但加工工艺相似，应基于熟制工艺特征匹配"
        },
        {
            "name": "名称相近但需特征确认 - 香肠类产品",
            "data": {
                "品名": "中式香肠",
                "原配料": "猪肉、盐、糖、白酒",
                "存储条件": "常温保存",
                "保质期": "90天",
                "净含量": "300g",
                "食用方法": "蒸煮后食用",
                "生产加工工艺": "灌制风干",
                "食品分类号": "02.01.01",
                "食品分类名称": "中式香肠",
                "食品原产地": "中国"
            },
            "expected_focus": "特征分析",
            "test_point": "应基于灌制工艺特征而非香肠名称进行匹配"
        },
        {
            "name": "液体乳制品 - 基于形态特征分析",
            "data": {
                "品名": "纯牛奶",
                "原配料": "生牛乳、维生素D",
                "存储条件": "冷藏保存",
                "保质期": "30天",
                "净含量": "1L",
                "食用方法": "直接饮用",
                "生产加工工艺": "超高温灭菌",
                "食品分类号": "01.01.01",
                "食品分类名称": "液体乳",
                "食品原产地": "新西兰"
            },
            "expected_focus": "特征分析",
            "test_point": "应基于液态形态和乳制品特征进行分析"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*80}")
        print(f"测试 {i}: {test_case['name']}")
        print(f"{'='*80}")
        
        print(f"📤 食品分类名称: {test_case['data']['食品分类名称']}")
        print(f"📍 原产地: {test_case['data']['食品原产地']}")
        print(f"🎯 预期分析重点: {test_case['expected_focus']}")
        print(f"🔍 测试要点: {test_case['test_point']}")
        
        try:
            response = requests.post(
                "http://localhost:8000/check-food-entry/",
                json=test_case['data'],
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"\n🎯 最终结果: {result.get('准入状态')} - {result.get('原因')}")
                
                # 查找映射信息
                mapping_info_found = False
                if "详细信息" in result and "映射查找结果" in result["详细信息"]:
                    映射查找结果 = result["详细信息"]["映射查找结果"]
                    映射记录 = 映射查找结果.get('映射记录')
                    
                    if 映射记录:
                        mapping_info_found = True
                        print(f"\n🗺️ 映射记录信息:")
                        print(f"  食品类别: {映射记录.get('食品类别', 'N/A')}")
                        print(f"  说明: {映射记录.get('说明', 'N/A')}")
                        print(f"  主要特征: {映射记录.get('主要特征', 'N/A')}")
                
                # 查找大模型分析结果
                llm_analysis_found = False
                reasoning_analysis = None
                
                if "详细信息" in result and "检查过程" in result["详细信息"]:
                    检查过程 = result["详细信息"]["检查过程"]
                    
                    for 步骤名, 步骤信息 in 检查过程.items():
                        if ("乳制品检查" in 步骤名 or "肉制品检查" in 步骤名) and isinstance(步骤信息, dict):
                            if "详情" in 步骤信息 and "大模型完整分类匹配结果" in 步骤信息["详情"]:
                                llm_analysis_found = True
                                大模型结果 = 步骤信息["详情"]["大模型完整分类匹配结果"]
                                reasoning_analysis = analyze_reasoning_focus(大模型结果)
                                
                                print(f"\n🤖 {步骤名} - 大模型分析:")
                                print(f"  是否允许: {大模型结果.get('is_allowed', 'N/A')}")
                                print(f"  匹配产品: {大模型结果.get('matched_product', 'N/A')}")
                                print(f"  置信度: {大模型结果.get('confidence', 'N/A')}")
                                
                                # 显示推理重点分析
                                print(f"\n📊 推理重点分析:")
                                print(f"  特征相关提及: {reasoning_analysis['feature_mentions']} 次")
                                print(f"  名称相关提及: {reasoning_analysis['name_mentions']} 次")
                                print(f"  分析重点: {reasoning_analysis['analysis']}")
                                print(f"  基于特征分析: {'✅ 是' if reasoning_analysis['focus_on_features'] else '❌ 否'}")
                                print(f"  受名称影响: {'⚠️ 是' if reasoning_analysis['focus_on_names'] else '✅ 否'}")
                                
                                # 显示详细分析的前几项
                                detailed_analysis = 大模型结果.get('detailed_analysis', [])
                                if detailed_analysis:
                                    print(f"\n📋 详细产品分析 (前3项):")
                                    for j, item in enumerate(detailed_analysis[:3], 1):
                                        print(f"  [{j}] {item.get('allowed_product', 'N/A')}")
                                        print(f"      匹配分数: {item.get('match_score', 'N/A')}")
                                        print(f"      分析理由: {item.get('match_reason', 'N/A')}")
                                        print(f"      是否匹配: {'✅' if item.get('is_match') else '❌'}")
                
                # 整体评估
                print(f"\n📈 基于特征分析评估:")
                print(f"  映射信息获取: {'✅ 成功' if mapping_info_found else '❌ 失败'}")
                print(f"  大模型分析执行: {'✅ 成功' if llm_analysis_found else '❌ 失败'}")
                
                if reasoning_analysis:
                    feature_focus_score = 1 if reasoning_analysis['focus_on_features'] else 0
                    name_avoidance_score = 1 if not reasoning_analysis['focus_on_names'] else 0
                    balance_score = 1 if reasoning_analysis['feature_mentions'] > reasoning_analysis['name_mentions'] else 0
                    
                    total_score = feature_focus_score + name_avoidance_score + balance_score
                    print(f"  重点关注特征: {'✅' if feature_focus_score else '❌'}")
                    print(f"  避免名称影响: {'✅' if name_avoidance_score else '❌'}")
                    print(f"  特征>名称权重: {'✅' if balance_score else '❌'}")
                    print(f"  整体评估: {'✅ 优秀' if total_score >= 3 else '⚠️ 良好' if total_score >= 2 else '❌ 需改进'} ({total_score}/3)")
                else:
                    print(f"  无法评估推理重点")
                
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(response.text)
                
        except Exception as e:
            print(f"💥 错误: {str(e)}")

def main():
    """主函数"""
    print("🧪 测试基于说明和特征的分析，验证不受名称影响")
    
    # 检查服务器
    try:
        response = requests.get("http://localhost:8000/health", timeout=3)
        if response.status_code == 200:
            print("✅ 服务器正常")
        else:
            print("⚠️ 服务器异常")
            return
    except:
        print("❌ 无法连接服务器")
        return
    
    test_feature_based_analysis()
    
    print(f"\n🎉 测试完成!")
    print(f"\n💡 基于特征分析的改进说明:")
    print(f"  - 大模型现在主要基于映射记录的'说明'和'主要特征'进行分析")
    print(f"  - 强调不要过度受具体名称影响")
    print(f"  - 重点关注产品的本质属性、加工工艺、形态特征")
    print(f"  - 提高了对名称不同但特征相似产品的识别能力")
    print(f"  - 避免因名称差异导致的误判")

if __name__ == "__main__":
    main()
