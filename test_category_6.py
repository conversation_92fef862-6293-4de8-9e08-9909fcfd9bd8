#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试映射类别号6（未深加工产品）的功能
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal
from app.schemas import FoodCheckRequest
from app.main import check_lightly_processed_product

async def test_category_6():
    """测试映射类别号6的检查逻辑"""
    db = SessionLocal()
    
    try:
        print("=== 测试映射类别号6（未深加工产品）检查逻辑 ===\n")
        
        # 测试用例1：有准入记录的国家
        print("1. 测试有准入记录的国家")
        request1 = FoodCheckRequest(
            食品原产地="美国",
            食品分类名称="咖啡豆制品",
            品名="烘焙咖啡豆",
            食品配料="咖啡豆, 天然香料",
            存储条件="常温"
        )
        
        配料列表1 = ["咖啡豆", "天然香料"]
        映射信息1 = {"食品分类名称": "咖啡豆制品"}
        
        result1 = await check_lightly_processed_product(db, request1, 配料列表1, 映射信息1)
        print(f"   结果: {'准入' if result1['allowed'] else '不准入'}")
        print(f"   原因: {result1['reason']}")
        print(f"   详情: 检查了{result1['details'].get('准入记录数', 0)}条准入记录")
        
        # 测试用例2：无准入记录的国家
        print("\n2. 测试无准入记录的国家")
        request2 = FoodCheckRequest(
            食品原产地="某个不存在的国家",
            食品分类名称="蜂蜜制品",
            品名="纯天然蜂蜜",
            食品配料="蜂蜜",
            存储条件="常温"
        )
        
        配料列表2 = ["蜂蜜"]
        映射信息2 = {"食品分类名称": "蜂蜜制品"}
        
        result2 = await check_lightly_processed_product(db, request2, 配料列表2, 映射信息2)
        print(f"   结果: {'准入' if result2['allowed'] else '不准入'}")
        print(f"   原因: {result2['reason']}")
        print(f"   详情: 检查了{result2['details'].get('准入记录数', 0)}条准入记录")
        
        # 测试用例3：有记录但配料不匹配
        print("\n3. 测试有记录但配料可能不匹配的情况")
        request3 = FoodCheckRequest(
            食品原产地="中国",
            食品分类名称="植物制品",
            品名="某种植物提取物",
            食品配料="植物提取物, 添加剂",
            存储条件="冷藏"
        )
        
        配料列表3 = ["植物提取物", "添加剂"]
        映射信息3 = {"食品分类名称": "植物制品"}
        
        result3 = await check_lightly_processed_product(db, request3, 配料列表3, 映射信息3)
        print(f"   结果: {'准入' if result3['allowed'] else '不准入'}")
        print(f"   原因: {result3['reason']}")
        print(f"   详情: 检查了{result3['details'].get('准入记录数', 0)}条准入记录")
        
        print("\n=== 测试完成 ===")
        print("✅ 映射类别号6的检查逻辑已实现，包括：")
        print("   - 查询8个准入名单表")
        print("   - 提取允许的产品名称")
        print("   - 调用大模型提取相关配料")
        print("   - 判断配料是否在允许产品列表中")
        print("   - 返回详细的检查结果")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_category_6())
