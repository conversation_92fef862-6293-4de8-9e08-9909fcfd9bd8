from pydantic import BaseModel
from typing import Optional, List, Dict, Any, Dict, Any

# 2760映射表
class MappingTableBase(BaseModel):
    分类号: Optional[str] = None
    食品类别: Optional[str] = None
    说明: Optional[str] = None
    主要特征: Optional[str] = None
    映射类别号: Optional[int] = None
    映射类别名称: Optional[str] = None

class MappingTableCreate(MappingTableBase):
    pass

class MappingTable(MappingTableBase):
    id: int
    
    class Config:
        from_attributes = True

# 三新食品原料数据库
class NewFoodMaterialBase(BaseModel):
    新食品原料名称: Optional[str] = None
    适宜人群: Optional[str] = None
    推荐食用量: Optional[str] = None
    使用范围和最大使用量: Optional[str] = None
    来源公告: Optional[str] = None
    质量要求: Optional[str] = None
    食品安全指标: Optional[str] = None

class NewFoodMaterialCreate(NewFoodMaterialBase):
    pass

class NewFoodMaterial(NewFoodMaterialBase):
    id: int
    
    class Config:
        from_attributes = True

# 中国食物成分表
class FoodCompositionBase(BaseModel):
    名称: Optional[str] = None

class FoodCompositionCreate(FoodCompositionBase):
    pass

class FoodComposition(FoodCompositionBase):
    id: int
    
    class Config:
        from_attributes = True

# 乳制品准入名单
class DairyAccessListBase(BaseModel):
    国家和地区: Optional[str] = None
    产品名称: Optional[str] = None
    准入状态: Optional[str] = None
    备注: Optional[str] = None

class DairyAccessListCreate(DairyAccessListBase):
    pass

class DairyAccessList(DairyAccessListBase):
    id: int
    
    class Config:
        from_attributes = True

# 加工助剂数据库
class ProcessingAidBase(BaseModel):
    加工助剂名称: Optional[str] = None
    英文名称: Optional[str] = None
    功能: Optional[str] = None
    使用范围: Optional[str] = None

class ProcessingAidCreate(ProcessingAidBase):
    pass

class ProcessingAid(ProcessingAidBase):
    id: int
    
    class Config:
        from_attributes = True

# 动植物源性药材准入名单
class MedicinalMaterialAccessBase(BaseModel):
    国家或地区: Optional[str] = None
    产品名称: Optional[str] = None
    产品描述: Optional[str] = None
    备注: Optional[str] = None

class MedicinalMaterialAccessCreate(MedicinalMaterialAccessBase):
    pass

class MedicinalMaterialAccess(MedicinalMaterialAccessBase):
    id: int
    
    class Config:
        from_attributes = True

# 动物疫情名单
class AnimalEpidemicBase(BaseModel):
    洲别: Optional[str] = None
    国家或地区: Optional[str] = None
    疫病: Optional[str] = None
    禁止进境物名称: Optional[str] = None

class AnimalEpidemicCreate(AnimalEpidemicBase):
    pass

class AnimalEpidemic(AnimalEpidemicBase):
    id: int
    
    class Config:
        from_attributes = True

# 可用于婴幼儿食品的菌种数据库
class InfantProbioticsBase(BaseModel):
    可用于婴幼儿食品的菌种名称: Optional[str] = None
    拉丁名称: Optional[str] = None
    公告链接: Optional[str] = None
    适用食品类别: Optional[str] = None
    对标签的标注要求: Optional[str] = None
    质量要求: Optional[str] = None

class InfantProbioticsCreate(InfantProbioticsBase):
    pass

class InfantProbiotics(InfantProbioticsBase):
    id: int
    
    class Config:
        from_attributes = True

# 可用于食品的菌种数据库
class FoodProbioticsBase(BaseModel):
    可用于食品的菌种名称: Optional[str] = None
    拉丁名称: Optional[str] = None
    不适宜人群: Optional[str] = None
    适用的食品类别: Optional[str] = None
    公告链接: Optional[str] = None
    标签要求: Optional[str] = None
    质量要求: Optional[str] = None

class FoodProbioticsCreate(FoodProbioticsBase):
    pass

class FoodProbiotics(FoodProbioticsBase):
    id: int
    
    class Config:
        from_attributes = True

# 咖啡豆和可可豆的允许准入名单
class CoffeeCocoaAccessBase(BaseModel):
    种类: Optional[str] = None
    国家和地区: Optional[str] = None

class CoffeeCocoaAccessCreate(CoffeeCocoaAccessBase):
    pass

class CoffeeCocoaAccess(CoffeeCocoaAccessBase):
    id: int
    
    class Config:
        from_attributes = True

# 新鲜水果准入名单
class FreshFruitAccessBase(BaseModel):
    分布: Optional[str] = None
    输出国家_地区: Optional[str] = None
    水果种类: Optional[Dict[str, Any]] = None

class FreshFruitAccessCreate(FreshFruitAccessBase):
    pass

class FreshFruitAccess(FreshFruitAccessBase):
    id: int
    
    class Config:
        from_attributes = True

# 新鲜蔬菜、香辛料等植物准入名单
class VegetableSpiceAccessBase(BaseModel):
    产品名称: Optional[str] = None
    国家和地区: Optional[str] = None
    备注: Optional[str] = None

class VegetableSpiceAccessCreate(VegetableSpiceAccessBase):
    pass

class VegetableSpiceAccess(VegetableSpiceAccessBase):
    id: int
    
    class Config:
        from_attributes = True

# 植物源性准入名单
class PlantOriginAccessBase(BaseModel):
    国家和地区: Optional[str] = None
    产品名称: Optional[str] = None
    准入状态: Optional[str] = None
    备注: Optional[str] = None

class PlantOriginAccessCreate(PlantOriginAccessBase):
    pass

class PlantOriginAccess(PlantOriginAccessBase):
    id: int
    
    class Config:
        from_attributes = True

# 水产品准入名单
class AquaticProductAccessBase(BaseModel):
    国家和地区: Optional[str] = None
    产品名称: Optional[str] = None
    准入状态: Optional[str] = None
    备注: Optional[str] = None

class AquaticProductAccessCreate(AquaticProductAccessBase):
    pass

class AquaticProductAccess(AquaticProductAccessBase):
    id: int
    
    class Config:
        from_attributes = True

# 添加剂数据库
class FoodAdditiveBase(BaseModel):
    添加剂名称: Optional[str] = None
    添加剂英文名称: Optional[str] = None
    CNS号: Optional[str] = None
    INS号: Optional[str] = None
    功能: Optional[str] = None
    食品分类号: Optional[str] = None
    食品名称: Optional[str] = None
    最大使用量g_kg: Optional[str] = None
    备注: Optional[str] = None

class FoodAdditiveCreate(FoodAdditiveBase):
    pass

class FoodAdditive(FoodAdditiveBase):
    id: int
    
    class Config:
        from_attributes = True

# 肉制品准入名单
class MeatProductAccessBase(BaseModel):
    地区: Optional[str] = None
    产品大类: Optional[str] = None
    品名: Optional[Dict[str, Any]] = None
    注册企业: Optional[Dict[str, Any]] = None
    状态: Optional[str] = None

class MeatProductAccessCreate(MeatProductAccessBase):
    pass

class MeatProductAccess(MeatProductAccessBase):
    id: int
    
    class Config:
        from_attributes = True

# 胶基数据库
class GumBaseBase(BaseModel):
    胶基配料: Optional[str] = None
    胶基配料的英文名称: Optional[str] = None
    所属类别: Optional[str] = None
    所属类别的英文名称: Optional[str] = None

class GumBaseCreate(GumBaseBase):
    pass

class GumBase(GumBaseBase):
    id: int
    
    class Config:
        from_attributes = True

# 药食同源名单
class FoodMedicineSameOriginBase(BaseModel):
    既是食品又是药品的物品名称: Optional[str] = None
    植物名: Optional[str] = None
    适用食品的类别: Optional[str] = None
    拉丁名称: Optional[str] = None
    公告链接: Optional[str] = None
    质量要求: Optional[str] = None
    对标签的标注要求: Optional[str] = None

class FoodMedicineSameOriginCreate(FoodMedicineSameOriginBase):
    pass

class FoodMedicineSameOrigin(FoodMedicineSameOriginBase):
    id: int
    
    class Config:
        from_attributes = True

# 营养强化剂数据库
class NutrientFortifierBase(BaseModel):
    营养强化剂名称: Optional[str] = None
    化合物来源: Optional[str] = None
    应用范围: Optional[str] = None
    备注: Optional[str] = None
    适用食品: Optional[str] = None
    在适用食品中的要求: Optional[str] = None

class NutrientFortifierCreate(NutrientFortifierBase):
    pass

class NutrientFortifier(NutrientFortifierBase):
    id: int
    
    class Config:
        from_attributes = True

# 进口粮食、动物饲料准入名单
class GrainFeedAccessBase(BaseModel):
    类型: Optional[str] = None
    种类: Optional[str] = None
    输出国家或地区: Optional[str] = None

class GrainFeedAccessCreate(GrainFeedAccessBase):
    pass

class GrainFeedAccess(GrainFeedAccessBase):
    id: int
    
    class Config:
        from_attributes = True

# 酶制剂数据库
class EnzymePreparationBase(BaseModel):
    酶制剂名称: Optional[str] = None
    英文名称: Optional[str] = None
    来源: Optional[str] = None
    供体: Optional[str] = None

class EnzymePreparationCreate(EnzymePreparationBase):
    pass

class EnzymePreparation(EnzymePreparationBase):
    id: int
    
    class Config:
        from_attributes = True

# 香精香料数据库
class FlavorSpiceBase(BaseModel):
    编码: Optional[str] = None
    香料中文名称: Optional[str] = None
    香料英文名称: Optional[str] = None
    FEMA编号: Optional[str] = None

class FlavorSpiceCreate(FlavorSpiceBase):
    pass

class FlavorSpice(FlavorSpiceBase):
    id: int

    class Config:
        from_attributes = True

# 食品准入检查请求模型
class FoodCheckRequest(BaseModel):
    品名: str
    原配料: str
    存储条件: str
    保质期: str
    净含量: str
    食用方法: str
    生产加工工艺: str
    食品分类号: str
    食品分类名称: str
    食品原产地: str

# 食品准入检查响应模型
class FoodCheckResponse(BaseModel):
    准入状态: str
    原因: str
    输入信息: Dict[str, Any]
    实际检查使用的信息: Dict[str, Any]
    检查路径: str
    详细信息: Dict[str, Any]