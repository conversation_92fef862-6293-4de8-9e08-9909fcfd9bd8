from fastapi import FastAPI, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Dict, Any
from pydantic import BaseModel

from .models import Base
from .database import engine, SessionLocal
from .schemas import *  # 导入所有模型
from .crud import *  # 导入所有CRUD函数
from .llm_service import get_llm_service
import re

# 创建数据库表
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="食品数据库检索系统",
    description="食品准入检查API",
    version="1.0.0"
)

# # 新增输入数据模型
# class FoodCheckRequest(BaseModel):
#     食品分类号: str
#     食品分类名称: str
#     食品原产地: str
#     食品配料: List[str]

# class FoodCheckResponse(BaseModel):
#     准入状态: str  # "准入" 或 "不准入"
#     原因: str = ""
#     详细信息: Dict[str, Any] = {}

# 依赖项
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "message": "食品数据库检索系统运行正常"
    }

@app.post("/check-food-entry/", response_model=FoodCheckResponse)
async def check_food_entry(request: FoodCheckRequest, db: Session = Depends(get_db)):
    """
    食品准入检查API
    """
    try:
        # 解析原配料字符串为配料列表
        配料列表 = request.原配料
        配料列表 = re.split(r'[、，,]', 配料列表)
        配料列表 = [配料.strip() for 配料 in 配料列表 if 配料.strip()]
        if not 配料列表:
            配料列表 = [配料.strip() for 配料 in request.原配料.split(',') if 配料.strip()]

        # 第一步：检查动物疫情
        epidemic_check = await check_animal_epidemic(db, request.食品原产地, 配料列表)

        if not epidemic_check["allowed"]:
            return FoodCheckResponse(
                准入状态="不准入",
                原因=epidemic_check["reason"],
                输入信息={
                    "品名": request.品名,
                    "原配料": request.原配料,
                    "存储条件": request.存储条件,
                    "保质期": request.保质期,
                    "净含量": request.净含量,
                    "食用方法": request.食用方法,
                    "生产加工工艺": request.生产加工工艺,
                    "食品分类号": request.食品分类号,
                    "食品分类名称": request.食品分类名称,
                    "食品原产地": request.食品原产地
                },
                实际检查使用的信息={
                    "食品分类号": request.食品分类号,
                    "食品分类名称": request.食品分类名称,
                    "食品原产地": request.食品原产地,
                    "解析后配料": 配料列表
                },
                检查路径="疫情检查失败",
                详细信息={
                    "检查过程": {
                        "步骤1_动物疫情检查": epidemic_check,
                        "步骤2_映射查找": "未执行（疫情检查失败）",
                        "步骤3_具体检查": "未执行（疫情检查失败）"
                    }
                }
            )

        # 第二步：根据映射类别号进行检查
        mapping_result = get_mapping_by_classification(db, request.食品分类号)

        if not mapping_result:
            return FoodCheckResponse(
                准入状态="不准入",
                原因="未找到对应的食品分类映射",
                输入信息={
                    "品名": request.品名,
                    "原配料": request.原配料,
                    "存储条件": request.存储条件,
                    "保质期": request.保质期,
                    "净含量": request.净含量,
                    "食用方法": request.食用方法,
                    "生产加工工艺": request.生产加工工艺,
                    "食品分类号": request.食品分类号,
                    "食品分类名称": request.食品分类名称,
                    "食品原产地": request.食品原产地
                },
                实际检查使用的信息={
                    "食品分类号": request.食品分类号,
                    "食品分类名称": request.食品分类名称,
                    "食品原产地": request.食品原产地,
                    "解析后配料": 配料列表
                },
                检查路径="疫情检查 → 映射查找失败",
                详细信息={
                    "检查过程": {
                        "步骤1_动物疫情检查": epidemic_check,
                        "步骤2_映射查找": {
                            "状态": "失败",
                            "原因": "未找到对应的食品分类映射",
                            "查找的分类号": request.食品分类号,
                            "映射记录": None
                        },
                        "步骤3_具体检查": "未执行（映射查找失败）"
                    }
                }
            )

        # 提取映射信息的所有字段
        映射类别号 = mapping_result.映射类别号
        映射信息 = {
            "分类号": mapping_result.分类号,
            "食品类别": mapping_result.食品类别,
            "说明": mapping_result.说明,
            "主要特征": mapping_result.主要特征,
            "映射类别号": mapping_result.映射类别号,
            "映射类别名称": mapping_result.映射类别名称
        }

        # 第三步：根据映射类别号进行具体检查
        if 映射类别号 == 1:
            result = await check_unprocessed_food(db, request, 配料列表)
        elif 映射类别号 == 2:
            result = await check_dairy_product(db, request, 配料列表, 映射信息)
        elif 映射类别号 == 3:
            result = await check_meat_product(db, request, 配料列表, 映射信息)
        elif 映射类别号 == 4:
            result = await check_aquatic_product(db, request, 配料列表, 映射信息)
        elif 映射类别号 == 5:
            result = {
                "allowed": True,
                "reason": "其他加工产品，允许准入",
                "details": {
                    "检查类型": "其他加工产品",
                    "映射类别号": 映射类别号,
                    "映射类别名称": mapping_result.映射类别名称
                }
            }
        elif 映射类别号 == 6:
            result = await check_lightly_processed_product(db, request, 配料列表, 映射信息)
        elif 映射类别号 == 7:
            result = await check_bird_nest_product(db, request, 映射信息)
        elif 映射类别号 == 8:
            result = {
                "allowed": False,
                "reason": "需人工审核",
                "details": {
                    "检查类型": "其他",
                    "映射类别号": 8,
                    "说明": "此类产品需要人工审核"
                }
            }
        else:
            result = {
                "allowed": False,
                "reason": f"未知的映射类别号: {映射类别号}",
                "details": {
                    "映射类别号": 映射类别号
                }
            }

        final_status = "准入" if result["allowed"] else "不准入"

        # 构建完整的检查过程信息
        检查过程 = {
            "步骤1_动物疫情检查": {
                "状态": "通过" if epidemic_check["allowed"] else "失败",
                "原因": epidemic_check["reason"],
                "详情": epidemic_check.get("details", {})
            },
            "步骤2_映射查找": {
                "状态": "成功",
                "查找的分类号": request.食品分类号,
                "映射记录": 映射信息,
                "映射类别号": 映射类别号,
                "映射类别名称": mapping_result.映射类别名称
            },
            "步骤3_具体检查": {
                "检查类型": mapping_result.映射类别名称,
                "状态": "通过" if result["allowed"] else "失败",
                "原因": result.get("reason", ""),
                "详情": result.get("details", {})
            }
        }

        return FoodCheckResponse(
            准入状态=final_status,
            原因=result.get("reason", ""),
            输入信息={
                "品名": request.品名,
                "原配料": request.原配料,
                "存储条件": request.存储条件,
                "保质期": request.保质期,
                "净含量": request.净含量,
                "食用方法": request.食用方法,
                "生产加工工艺": request.生产加工工艺,
                "食品分类号": request.食品分类号,
                "食品分类名称": request.食品分类名称,
                "食品原产地": request.食品原产地
            },
            实际检查使用的信息={
                "食品分类号": request.食品分类号,
                "食品分类名称": request.食品分类名称,
                "食品原产地": request.食品原产地,
                "解析后配料": 配料列表
            },
            检查路径=f"疫情检查 → 映射查找 → {mapping_result.映射类别名称}检查",
            详细信息={
                "检查过程": 检查过程
            }
        )

    except Exception as e:
        return FoodCheckResponse(
            准入状态="不准入",
            原因=f"检查过程中发生错误: {str(e)}",
            输入信息={
                "品名": request.品名,
                "原配料": request.原配料,
                "存储条件": request.存储条件,
                "保质期": request.保质期,
                "净含量": request.净含量,
                "食用方法": request.食用方法,
                "生产加工工艺": request.生产加工工艺,
                "食品分类号": request.食品分类号,
                "食品分类名称": request.食品分类名称,
                "食品原产地": request.食品原产地
            },
            实际检查使用的信息={
                "食品分类号": request.食品分类号,
                "食品分类名称": request.食品分类名称,
                "食品原产地": request.食品原产地,
                "解析后配料": []
            },
            检查路径="系统异常",
            详细信息={
                "错误信息": str(e),
                "检查过程": "系统发生异常，检查中断"
            }
        )

async def check_animal_epidemic(db: Session, 原产地: str, 配料列表: List[str]) -> Dict[str, Any]:
    """检查动物疫情"""
    try:
        # 输入验证
        if not 原产地 or not isinstance(原产地, str):
            return {"allowed": True, "reason": "原产地信息无效"}

        if not 配料列表 or not isinstance(配料列表, list):
            return {"allowed": True, "reason": "配料列表为空或无效"}

        # 过滤有效配料
        valid_配料 = [str(item).strip() for item in 配料列表 if item and str(item).strip()]
        if not valid_配料:
            return {"allowed": True, "reason": "没有有效的配料信息"}

        # 查找动物疫情名单中的相关国家
        epidemic_records = get_animal_epidemic_by_country(db, 原产地.strip())

        if not epidemic_records:
            return {"allowed": True, "reason": "该国家无动物疫情记录"}

        # 提取禁止进境物名称
        prohibited_items = []
        for record in epidemic_records:
            if hasattr(record, '禁止进境物名称') and record.禁止进境物名称:
                prohibited_items.append(str(record.禁止进境物名称).strip())

        if not prohibited_items:
            return {"allowed": True, "reason": "该国家无具体禁止进境物"}

        # 调用大模型判断配料是否属于禁止进境物
        try:
            llm_service = get_llm_service()
            llm_result = await llm_service.check_ingredient_in_prohibited_items(valid_配料, prohibited_items)

            if llm_result and llm_result.get("has_prohibited", False):
                matched_items = llm_result.get("matched_items", [])
                if matched_items and len(matched_items) > 0:
                    matched_item = matched_items[0]  # 取第一个匹配项
                    return {
                        "allowed": False,
                        "reason": f"存在动物疫情，配料'{matched_item.get('ingredient', '未知')}'属于禁止进境物'{matched_item.get('prohibited_item', '未知')}'",
                        "details": {
                            "配料": matched_item.get('ingredient', '未知'),
                            "配料类型": matched_item.get('category', '未知'),
                            "禁止物": matched_item.get('prohibited_item', '未知'),
                            "原产地": 原产地,
                            "匹配原因": matched_item.get('reason', '未提供原因'),
                            "所有疫情记录": [{"疫病": getattr(r, '疫病', '未知'), "禁止物": getattr(r, '禁止进境物名称', '未知')} for r in epidemic_records],
                            "大模型完整分析结果": llm_result
                        }
                    }

            return {
                "allowed": True,
                "reason": "配料不属于禁止进境物",
                "details": {
                    "疫情记录数": len(epidemic_records),
                    "疫情记录详情": [{"疫病": getattr(r, '疫病', '未知'), "禁止进境物": getattr(r, '禁止进境物名称', '未知')} for r in epidemic_records],
                    "禁止进境物列表": prohibited_items,
                    "大模型完整分析结果": llm_result
                }
            }

        except Exception as llm_error:
            # 如果大模型调用失败，采用简单的字符串匹配作为备选方案
            for 配料 in valid_配料:
                for 禁止物 in prohibited_items:
                    if 配料 and 禁止物 and (配料 in 禁止物 or 禁止物 in 配料):
                        return {
                            "allowed": False,
                            "reason": f"存在动物疫情，配料'{配料}'可能属于禁止进境物'{禁止物}'（字符串匹配）",
                            "details": {
                                "配料": 配料,
                                "禁止物": 禁止物,
                                "原产地": 原产地,
                                "匹配方式": "字符串匹配（大模型不可用）",
                                "错误信息": str(llm_error)
                            }
                        }

            return {"allowed": True, "reason": "配料不属于禁止进境物（字符串匹配检查）"}

    except Exception as e:
        # 最终的错误处理
        return {
            "allowed": True,
            "reason": f"动物疫情检查过程中发生错误，默认允许：{str(e)}",
            "details": {"error": str(e)}
        }

async def check_unprocessed_food(db: Session, request: FoodCheckRequest, 配料列表: List[str]) -> Dict[str, Any]:
    """检查未经加工食品"""
    食品原产地 = request.食品原产地

    # 第一步：查找多个准入名单（不包括水产品）
    access_lists = []
    access_lists.extend(get_coffee_cocoa_access_by_country(db, 食品原产地))
    access_lists.extend(get_meat_product_access_by_country(db, 食品原产地))
    access_lists.extend(get_plant_origin_access_by_country(db, 食品原产地))
    access_lists.extend(get_medicinal_material_access_by_country(db, 食品原产地))
    access_lists.extend(get_fresh_fruit_access_by_country(db, 食品原产地))
    access_lists.extend(get_vegetable_spice_access_by_country(db, 食品原产地))
    access_lists.extend(get_grain_feed_access_by_country(db, 食品原产地))
    # 新增的3个准入名单
    access_lists.extend(get_casing_access_by_country(db, 食品原产地))
    access_lists.extend(get_bee_product_access_by_country(db, 食品原产地))
    access_lists.extend(get_bird_nest_access_by_country(db, 食品原产地))

    if not access_lists:
        return {
            "allowed": False,
            "reason": f"不允许从{食品原产地}进口",
            "details": {
                "检查类型": "未经加工食品",
                "原产地": 食品原产地,
                "准入记录数": 0,
                "查询结果": "未找到该国家的准入记录"
            }
        }

    # 第二步：提取允许的产品名称（确保扁平化）
    allowed_products = []
    access_records_details = []

    for record in access_lists:
        # 构建详细记录信息
        record_detail = {
            "来源表": record.__tablename__ if hasattr(record, '__tablename__') else '未知',
            "国家或地区": getattr(record, '国家或地区', ''),
            "产品信息": {}
        }

        # 处理产品名称字段
        if hasattr(record, '产品名称') and record.产品名称:
            record_detail["产品信息"]["产品名称"] = record.产品名称
            if isinstance(record.产品名称, list):
                allowed_products.extend([str(item).strip() for item in record.产品名称 if item and str(item).strip()])
            else:
                allowed_products.append(str(record.产品名称).strip())

        # 处理种类字段
        if hasattr(record, '种类') and record.种类:
            record_detail["产品信息"]["种类"] = record.种类
            if isinstance(record.种类, list):
                allowed_products.extend([str(item).strip() for item in record.种类 if item and str(item).strip()])
            else:
                allowed_products.append(str(record.种类).strip())

        # 处理品名字段
        if hasattr(record, '品名') and record.品名:
            record_detail["产品信息"]["品名"] = record.品名
            if isinstance(record.品名, dict):
                # 处理字典类型的品名，确保扁平化
                for value in record.品名.values():
                    if isinstance(value, list):
                        allowed_products.extend([str(item).strip() for item in value if item and str(item).strip()])
                    elif value and str(value).strip():
                        allowed_products.append(str(value).strip())
            elif isinstance(record.品名, list):
                allowed_products.extend([str(item).strip() for item in record.品名 if item and str(item).strip()])
            else:
                allowed_products.append(str(record.品名).strip())

        access_records_details.append(record_detail)

    # 去重并过滤空值
    allowed_products = list(set([product for product in allowed_products if product]))

    # 第三步：使用大模型检查每个配料是否在允许列表中
    llm_service = get_llm_service()
    配料检查结果 = []

    for 配料 in 配料列表:
        if not 配料 or not str(配料).strip():
            continue  # 跳过空配料

        配料_str = str(配料).strip()

        # 调用大模型判断配料是否在允许产品列表中
        ingredient_result = await llm_service.check_ingredient_in_allowed_products(配料_str, allowed_products)

        配料检查结果.append({
            "配料": 配料_str,
            "检查结果": ingredient_result
        })

        # 如果任意一个配料不存在，不准入
        if not ingredient_result.get("is_allowed", False):
            return {
                "allowed": False,
                "reason": f"不允许从{食品原产地}进口该产品：{配料_str}",
                "details": {
                    "检查类型": "未经加工食品",
                    "原产地": 食品原产地,
                    "不允许的配料": 配料_str,
                    "配料检查结果": ingredient_result,
                    "准入记录数": len(access_lists),
                    "准入记录详情": access_records_details,
                    "总允许产品数": len(allowed_products),
                    "允许产品列表": allowed_products[:50]
                }
            }

    # 第四步：所有配料都存在，准入
    return {
        "allowed": True,
        "reason": "所有配料都在允许进口列表中",
        "details": {
            "检查类型": "未经加工食品",
            "原产地": 食品原产地,
            "配料检查结果": 配料检查结果,
            "准入记录数": len(access_lists),
            "准入记录详情": access_records_details,
            "总允许产品数": len(allowed_products),
            "允许产品列表": allowed_products[:50],
            "准入记录来源统计": {
                "咖啡可可准入": len(get_coffee_cocoa_access_by_country(db, 食品原产地)),
                "肉制品准入": len(get_meat_product_access_by_country(db, 食品原产地)),
                "植物源性准入": len(get_plant_origin_access_by_country(db, 食品原产地)),
                "药材准入": len(get_medicinal_material_access_by_country(db, 食品原产地)),
                "新鲜水果准入": len(get_fresh_fruit_access_by_country(db, 食品原产地)),
                "蔬菜香料准入": len(get_vegetable_spice_access_by_country(db, 食品原产地)),
                "粮食饲料准入": len(get_grain_feed_access_by_country(db, 食品原产地))
            }
        }
    }

async def check_aquatic_product(db: Session, request: FoodCheckRequest, 配料列表: List[str], 映射信息: Dict[str, Any] = None) -> Dict[str, Any]:
    """检查水产品"""
    食品原产地 = request.食品原产地
    存储条件 = request.存储条件

    # 第一步：查找水产品准入名单中的国家和地区字段
    aquatic_records = get_aquatic_product_access_by_country(db, 食品原产地)

    if not aquatic_records:
        return {
            "allowed": False,
            "reason": f"不允许从{食品原产地}进口水产品",
            "details": {
                "检查类型": "水产品",
                "原产地": 食品原产地,
                "存储条件": 存储条件,
                "水产品准入记录数": 0,
                "查询结果": "未找到该国家的水产品准入记录"
            }
        }

    # 第二步：提取产品名称字段
    allowed_products = []
    aquatic_records_details = []

    for record in aquatic_records:
        # 构建详细记录信息
        record_detail = {
            "产品名称": getattr(record, '产品名称', ''),
            "国家或地区": getattr(record, '国家或地区', ''),
            "准入状态": getattr(record, '准入状态', ''),
            "存储条件要求": "[冷冻]" if '[冷冻]' in getattr(record, '产品名称', '') else "鲜水产和冷冻水产品均可"
        }
        aquatic_records_details.append(record_detail)

        if record.产品名称:
            allowed_products.append(record.产品名称)

    if not allowed_products:
        return {
            "allowed": False,
            "reason": f"不允许从{食品原产地}进口水产品",
            "details": {
                "检查类型": "水产品",
                "原产地": 食品原产地,
                "存储条件": 存储条件,
                "水产品准入记录数": len(aquatic_records),
                "水产品准入记录详情": aquatic_records_details,
                "查询结果": "该国家的水产品准入记录中无有效产品"
            }
        }

    # 第三步：调用大模型提取食品配料字段中水产品相关的配料
    llm_service = get_llm_service()
    aquatic_ingredients = await llm_service.extract_aquatic_ingredients(配料列表)

    # 第四步：判断水产品配料是否属于允许产品列表中，同时检查存储条件
    if aquatic_ingredients:
        category_result = await llm_service.check_aquatic_ingredients_match(
            aquatic_ingredients,
            allowed_products,  # 使用所有允许的产品列表
            存储条件  # 传入存储条件
        )

        if not category_result.get("is_allowed", False):
            # 提取配料名称用于错误信息
            ingredient_names = [ing.get('ingredient', '') for ing in aquatic_ingredients]

            # 根据失败原因确定错误信息
            storage_check = category_result.get("storage_check", "")
            if storage_check == "fail":
                reason = f"存储条件不符合要求：{category_result.get('storage_reason', '')}"
            else:
                reason = f"不允许从{食品原产地}进口该产品：{', '.join(ingredient_names)}"

            return {
                "allowed": False,
                "reason": reason,
                "details": {
                    "检查类型": "水产品",
                    "原产地": 食品原产地,
                    "存储条件": 存储条件,
                    "水产品相关配料": aquatic_ingredients,
                    "大模型完整匹配和存储检查结果": category_result,
                    "匹配说明": "优先使用拉丁文学名进行匹配，同时检查存储条件",
                    "水产品准入记录数": len(aquatic_records),
                    "水产品准入记录详情": aquatic_records_details,
                    "允许的产品数": len(allowed_products),
                    "允许的产品列表": allowed_products
                }
            }

    # 第五步：产品匹配成功且存储条件符合，准入
    return {
        "allowed": True,
        "reason": "符合水产品准入要求和存储条件要求",
        "details": {
            "检查类型": "水产品",
            "原产地": 食品原产地,
            "存储条件": 存储条件,
            "水产品相关配料": aquatic_ingredients,
            "大模型完整匹配和存储检查结果": category_result if aquatic_ingredients else "无水产品配料",
            "匹配说明": "优先使用拉丁文学名进行匹配，同时检查存储条件",
            "水产品准入记录数": len(aquatic_records),
            "水产品准入记录详情": aquatic_records_details,
            "允许的产品数": len(allowed_products),
            "允许的产品列表": allowed_products
        }
    }

async def check_dairy_product(db: Session, request: FoodCheckRequest, 配料列表: List[str], 映射信息: Dict[str, Any] = None) -> Dict[str, Any]:
    """检查乳制品"""
    食品原产地 = request.食品原产地
    食品分类名称 = request.食品分类名称

    # 第一步：查找乳制品准入名单中的国家或地区
    dairy_records = get_dairy_access_by_country(db, 食品原产地)

    if not dairy_records:
        return {
            "allowed": False,
            "reason": f"不允许从{食品原产地}进口乳制品",
            "details": {
                "检查类型": "乳制品",
                "原产地": 食品原产地,
                "乳制品准入记录数": 0,
                "查询结果": "未找到该国家的乳制品准入记录"
            }
        }

    # 第二步：提取产品名称和备注字段
    allowed_products = []
    product_remarks = {}
    dairy_records_details = []

    for record in dairy_records:
        # 构建详细记录信息
        record_detail = {
            "产品名称": getattr(record, '产品名称', ''),
            "备注": getattr(record, '备注', ''),
            "准入状态": getattr(record, '准入状态', ''),
            "国家或地区": getattr(record, '国家或地区', '')
        }
        dairy_records_details.append(record_detail)

        if record.产品名称:
            allowed_products.append(record.产品名称)
            if hasattr(record, '备注') and record.备注:
                product_remarks[record.产品名称] = record.备注

    if not allowed_products:
        return {
            "allowed": False,
            "reason": f"不允许从{食品原产地}进口乳制品",
            "details": {
                "检查类型": "乳制品",
                "原产地": 食品原产地,
                "乳制品准入记录数": len(dairy_records),
                "乳制品准入记录详情": dairy_records_details,
                "查询结果": "该国家的乳制品准入记录中无有效产品"
            }
        }

    # 第三步：调用大模型判断食品分类名称是否属于列表中的相关产品
    llm_service = get_llm_service()
    category_result = await llm_service.check_food_category_match(食品分类名称, allowed_products, 映射信息)

    if not category_result.get("is_allowed", False):
        return {
            "allowed": False,
            "reason": f"不允许从{食品原产地}进口该产品：{食品分类名称}",
            "details": {
                "检查类型": "乳制品",
                "原产地": 食品原产地,
                "食品分类名称": 食品分类名称,
                "乳制品准入记录数": len(dairy_records),
                "乳制品准入记录详情": dairy_records_details,
                "允许的产品数": len(allowed_products),
                "允许的产品列表": allowed_products,
                "大模型完整分类匹配结果": category_result
            }
        }

    # 第四步：判断备注信息是否为空
    matched_product = category_result.get("matched_product")
    remark = product_remarks.get(matched_product, "")

    # 备注信息为空，准入
    if not remark:
        return {
            "allowed": True,
            "reason": "符合乳制品准入要求",
            "details": {
                "检查类型": "乳制品",
                "原产地": 食品原产地,
                "匹配的产品": matched_product,
                "备注检查": "无特殊要求",
                "乳制品准入记录数": len(dairy_records),
                "乳制品准入记录详情": dairy_records_details,
                "大模型完整分类匹配结果": category_result
            }
        }

    # 第五步：备注信息不为空，提取乳相关配料并判断是否满足备注要求
    dairy_ingredients = await llm_service.extract_dairy_ingredients(配料列表)

    # 调用大模型判断配料是否满足备注要求
    remark_result = await llm_service.check_remark_compliance(dairy_ingredients, remark)

    if remark_result.get("compliant", False):
        return {
            "allowed": True,
            "reason": "符合乳制品准入要求和备注要求",
            "details": {
                "检查类型": "乳制品",
                "原产地": 食品原产地,
                "匹配的产品": matched_product,
                "备注信息": remark,
                "乳相关配料": dairy_ingredients,
                "备注检查结果": remark_result,
                "乳制品准入记录数": len(dairy_records),
                "乳制品准入记录详情": dairy_records_details,
                "大模型完整分类匹配结果": category_result
            }
        }
    else:
        return {
            "allowed": False,
            "reason": remark_result.get("reason", "不满足备注要求"),
            "details": {
                "检查类型": "乳制品",
                "原产地": 食品原产地,
                "匹配的产品": matched_product,
                "备注信息": remark,
                "乳相关配料": dairy_ingredients,
                "备注检查结果": remark_result,
                "乳制品准入记录数": len(dairy_records),
                "乳制品准入记录详情": dairy_records_details,
                "大模型完整分类匹配结果": category_result
            }
        }

async def check_meat_product(db: Session, request: FoodCheckRequest, 配料列表: List[str], 映射信息: Dict[str, Any] = None) -> Dict[str, Any]:
    """检查肉制品"""
    原产地 = request.食品原产地
    食品分类名称 = request.食品分类名称
    # 注意：当前肉制品检查主要基于食品分类名称，配料列表暂未使用

    # 查找肉制品准入名单
    meat_records = get_meat_product_access_by_country(db, 原产地)

    if not meat_records:
        return {
            "allowed": False,
            "reason": "不允许从该国家进口肉制品",
            "details": {
                "肉制品准入记录数": 0,
                "说明": f"未找到 '{原产地}' 的肉制品准入记录"
            }
        }

    # 提取允许的产品名称（确保扁平化）
    allowed_products = []
    for record in meat_records:
        if record.品名 and isinstance(record.品名, dict):
            # 处理字典类型的品名，确保扁平化
            for value in record.品名.values():
                if isinstance(value, list):
                    # 如果值是列表，展开它
                    allowed_products.extend([str(item).strip() for item in value if item and str(item).strip()])
                elif value and str(value).strip():
                    # 如果值是字符串，直接添加
                    allowed_products.append(str(value).strip())
        elif record.品名:
            if isinstance(record.品名, list):
                # 如果品名本身是列表，展开它
                allowed_products.extend([str(item).strip() for item in record.品名 if item and str(item).strip()])
            else:
                # 如果品名是字符串，直接添加
                allowed_products.append(str(record.品名).strip())

    # 去重并过滤空值
    allowed_products = list(set([product for product in allowed_products if product]))

    # 调用大模型判断食品分类名称是否属于允许的产品
    llm_service = get_llm_service()
    category_result = await llm_service.check_food_category_match(食品分类名称, allowed_products, 映射信息)

    if not category_result["is_allowed"]:
        return {
            "allowed": False,
            "reason": f"不允许从该国家进口该产品：{食品分类名称}",
            "details": {
                "大模型完整分类匹配结果": category_result,
                "允许的产品数": len(allowed_products),
                "允许的产品列表": allowed_products
            }
        }

    return {
        "allowed": True,
        "reason": "肉制品检查通过",
        "details": {
            "肉制品准入记录数": len(meat_records),
            "允许的产品列表": allowed_products,
            "大模型完整分类匹配结果": category_result,
            "肉制品准入记录详情": [{"地区": getattr(r, '地区', '未知'), "产品大类": getattr(r, '产品大类', '未知'), "品名": getattr(r, '品名', '未知')} for r in meat_records]
        }
    }

async def check_lightly_processed_product(db: Session, request: FoodCheckRequest, 配料列表: List[str], 映射信息: Dict[str, Any] = None) -> Dict[str, Any]:
    """检查未深加工产品（映射类别号6）"""
    食品原产地 = request.食品原产地
    食品分类名称 = 映射信息.get("食品类别", "") if 映射信息 else ""

    # 第一步：查找多个准入名单
    access_lists = []
    access_lists.extend(get_coffee_cocoa_access_by_country(db, 食品原产地))
    access_lists.extend(get_bee_product_access_by_country(db, 食品原产地))
    access_lists.extend(get_casing_access_by_country(db, 食品原产地))
    access_lists.extend(get_plant_origin_access_by_country(db, 食品原产地))
    access_lists.extend(get_medicinal_material_access_by_country(db, 食品原产地))
    access_lists.extend(get_fresh_fruit_access_by_country(db, 食品原产地))
    access_lists.extend(get_vegetable_spice_access_by_country(db, 食品原产地))
    access_lists.extend(get_grain_feed_access_by_country(db, 食品原产地))

    # 第二步：提取允许的产品名称
    allowed_products = []
    access_records_details = []

    for record in access_lists:
        # 构建详细记录信息
        record_detail = {
            "来源表": record.__tablename__ if hasattr(record, '__tablename__') else '未知',
            "国家或地区": getattr(record, '国家或地区', '') or getattr(record, '国家和地区', '') or getattr(record, '输出国家_地区', '') or getattr(record, '输出国家或地区', ''),
            "产品信息": {}
        }

        # 处理不同表的产品名称字段
        product_name = None
        if hasattr(record, '产品名称') and record.产品名称:
            product_name = record.产品名称
            record_detail["产品信息"]["产品名称"] = product_name
        elif hasattr(record, '种类') and record.种类:
            product_name = record.种类
            record_detail["产品信息"]["种类"] = product_name
        elif hasattr(record, '品名') and record.品名:
            product_name = record.品名
            record_detail["产品信息"]["品名"] = product_name
        elif hasattr(record, '水果种类') and record.水果种类:
            product_name = record.水果种类
            record_detail["产品信息"]["水果种类"] = product_name

        # 处理产品名称（可能是字符串、列表或字典）
        if product_name:
            if isinstance(product_name, str):
                allowed_products.append(product_name.strip())
            elif isinstance(product_name, list):
                allowed_products.extend([str(item).strip() for item in product_name if item and str(item).strip()])
            elif isinstance(product_name, dict):
                for value in product_name.values():
                    if isinstance(value, list):
                        allowed_products.extend([str(item).strip() for item in value if item and str(item).strip()])
                    elif value and str(value).strip():
                        allowed_products.append(str(value).strip())

        access_records_details.append(record_detail)

    # 去重并过滤空值
    allowed_products = list(set([product for product in allowed_products if product]))

    if not allowed_products:
        return {
            "allowed": False,
            "reason": f"不允许从{食品原产地}进口该类未深加工产品",
            "details": {
                "检查类型": "未深加工产品",
                "映射类别号": 6,
                "原产地": 食品原产地,
                "食品分类名称": 食品分类名称,
                "准入记录数": len(access_lists),
                "准入记录详情": access_records_details,
                "查询结果": "该国家的准入记录中无有效产品"
            }
        }

    # 第三步：调用大模型提取食品配料字段中与食品分类名称相关的配料
    llm_service = get_llm_service()
    related_ingredients = await llm_service.extract_category_related_ingredients(
        配料列表, 食品分类名称
    )

    # 第四步：判断提取的相关配料是否在允许产品列表中
    if related_ingredients:
        category_result = await llm_service.check_ingredients_in_allowed_products(
            related_ingredients, allowed_products
        )

        if not category_result.get("is_allowed", False):
            return {
                "allowed": False,
                "reason": f"不允许从{食品原产地}进口该产品：{category_result.get('failed_ingredients', [])}",
                "details": {
                    "检查类型": "未深加工产品",
                    "映射类别号": 6,
                    "原产地": 食品原产地,
                    "食品分类名称": 食品分类名称,
                    "原始配料": 配料列表,
                    "提取的相关配料": related_ingredients,
                    "大模型完整匹配结果": category_result,
                    "准入记录数": len(access_lists),
                    "准入记录详情": access_records_details,
                    "允许的产品数": len(allowed_products)
                }
            }

    # 第五步：所有相关配料都符合要求，准入
    return {
        "allowed": True,
        "reason": "符合未深加工产品准入要求",
        "details": {
            "检查类型": "未深加工产品",
            "映射类别号": 6,
            "原产地": 食品原产地,
            "食品分类名称": 食品分类名称,
            "原始配料": 配料列表,
            "提取的相关配料": related_ingredients,
            "大模型完整匹配结果": category_result if related_ingredients else "无相关配料需要检查",
            "准入记录数": len(access_lists),
            "准入记录详情": access_records_details,
            "允许的产品数": len(allowed_products)
        }
    }

async def check_bird_nest_product(db: Session, request: FoodCheckRequest, 映射信息: Dict[str, Any] = None) -> Dict[str, Any]:
    """检查燕窝产品（映射类别号7）"""
    食品原产地 = request.食品原产地
    食品类别 = 映射信息.get("食品类别", "") if 映射信息 else ""

    # 第一步：查找燕窝准入名单
    bird_nest_records = get_bird_nest_access_by_country(db, 食品原产地)

    # 第二步：提取允许的产品名称
    allowed_products = []
    access_records_details = []

    for record in bird_nest_records:
        # 构建详细记录信息
        record_detail = {
            "来源表": "燕窝准入名单",
            "国家或地区": getattr(record, '国家或地区', ''),
            "产品信息": {}
        }

        # 处理产品名称字段
        product_name = None
        if hasattr(record, '产品名称') and record.产品名称:
            product_name = record.产品名称
            record_detail["产品信息"]["产品名称"] = product_name

        # 处理产品名称（可能是字符串、列表或字典）
        if product_name:
            if isinstance(product_name, str):
                allowed_products.append(product_name.strip())
            elif isinstance(product_name, list):
                allowed_products.extend([str(item).strip() for item in product_name if item and str(item).strip()])
            elif isinstance(product_name, dict):
                for value in product_name.values():
                    if isinstance(value, list):
                        allowed_products.extend([str(item).strip() for item in value if item and str(item).strip()])
                    elif value and str(value).strip():
                        allowed_products.append(str(value).strip())

        access_records_details.append(record_detail)

    # 去重并过滤空值
    allowed_products = list(set([product for product in allowed_products if product]))

    if not allowed_products:
        return {
            "allowed": False,
            "reason": f"不允许从{食品原产地}进口燕窝产品",
            "details": {
                "检查类型": "燕窝",
                "映射类别号": 7,
                "原产地": 食品原产地,
                "食品类别": 食品类别,
                "准入记录数": len(bird_nest_records),
                "准入记录详情": access_records_details,
                "查询结果": "该国家无燕窝准入记录"
            }
        }

    # 第三步：判断映射的食品类别是否在允许产品列表中存在
    category_found = False
    matched_product = None

    # 直接匹配食品类别
    for product in allowed_products:
        if 食品类别 and (食品类别.lower() in product.lower() or product.lower() in 食品类别.lower()):
            category_found = True
            matched_product = product
            break

    # 如果直接匹配失败，尝试模糊匹配
    if not category_found and 食品类别:
        for product in allowed_products:
            # 检查是否包含燕窝相关关键词
            if any(keyword in product.lower() for keyword in ['燕窝', 'bird', 'nest']):
                category_found = True
                matched_product = product
                break

    if not category_found:
        return {
            "allowed": False,
            "reason": f"不允许从{食品原产地}进口该燕窝产品：{食品类别}",
            "details": {
                "检查类型": "燕窝",
                "映射类别号": 7,
                "原产地": 食品原产地,
                "食品类别": 食品类别,
                "准入记录数": len(bird_nest_records),
                "准入记录详情": access_records_details,
                "允许的产品列表": allowed_products,
                "匹配结果": "食品类别未在允许产品列表中找到"
            }
        }

    # 第四步：食品类别存在于允许产品列表中，准入
    return {
        "allowed": True,
        "reason": "符合燕窝产品准入要求",
        "details": {
            "检查类型": "燕窝",
            "映射类别号": 7,
            "原产地": 食品原产地,
            "食品类别": 食品类别,
            "匹配的产品": matched_product,
            "准入记录数": len(bird_nest_records),
            "准入记录详情": access_records_details,
            "允许的产品列表": allowed_products
        }
    }

def get_mapping_by_classification(db: Session, 分类号: str):
    """根据分类号获取映射信息"""
    return db.query(MappingTable).filter(MappingTable.分类号 == 分类号).first()
