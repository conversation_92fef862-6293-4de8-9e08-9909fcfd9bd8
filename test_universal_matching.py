#!/usr/bin/env python3
"""
测试通用匹配策略的效果
"""

import requests
import json

def analyze_matching_strategy(analysis_result):
    """分析匹配策略是否体现了通用性"""
    if not isinstance(analysis_result, dict):
        return {"universal_approach": False, "analysis": "无法分析"}
    
    reason = analysis_result.get('reason', '').lower()
    detailed_analysis = analysis_result.get('detailed_analysis', [])
    
    # 检查是否体现了通用匹配策略
    universal_keywords = [
        '工艺', '类别', '形态', '原料', '功能', '本质', '属性', 
        '等效', '相同', '相似', '归属', '特征', '性质'
    ]
    
    specific_keywords = [
        '火腿', '香肠', '牛奶', '具体', '特定', '专门'
    ]
    
    universal_mentions = sum(1 for keyword in universal_keywords if keyword in reason)
    specific_mentions = sum(1 for keyword in specific_keywords if keyword in reason)
    
    # 检查详细分析中的通用性体现
    detailed_universal = 0
    detailed_specific = 0
    
    for item in detailed_analysis:
        match_reason = item.get('match_reason', '').lower()
        detailed_universal += sum(1 for keyword in universal_keywords if keyword in match_reason)
        detailed_specific += sum(1 for keyword in specific_keywords if keyword in match_reason)
    
    total_universal = universal_mentions + detailed_universal
    total_specific = specific_mentions + detailed_specific
    
    return {
        "universal_approach": total_universal > 0,
        "universal_mentions": total_universal,
        "specific_mentions": total_specific,
        "analysis": "体现通用策略" if total_universal > total_specific else "偏向具体匹配" if total_specific > total_universal else "策略平衡"
    }

def test_universal_matching():
    """测试通用匹配策略"""
    
    test_cases = [
        {
            "name": "肉制品 - 测试工艺等效匹配",
            "data": {
                "品名": "西式火腿",
                "原配料": "猪肉、盐、糖、香辛料",
                "存储条件": "冷藏保存",
                "保质期": "60天",
                "净含量": "500g",
                "食用方法": "切片食用",
                "生产加工工艺": "熏烤制作",
                "食品分类号": "08.03.04",
                "食品分类名称": "西式火腿(熏烤、烟熏、蒸煮火腿)类",
                "食品原产地": "德国"
            },
            "expected_strategy": "工艺等效匹配",
            "test_point": "蒸煮工艺应等效于熟制工艺"
        },
        {
            "name": "乳制品 - 测试类别层次匹配",
            "data": {
                "品名": "纯牛奶",
                "原配料": "生牛乳、维生素D",
                "存储条件": "冷藏保存",
                "保质期": "30天",
                "净含量": "1L",
                "食用方法": "直接饮用",
                "生产加工工艺": "超高温灭菌",
                "食品分类号": "01.01.01",
                "食品分类名称": "液体乳",
                "食品原产地": "新西兰"
            },
            "expected_strategy": "类别层次匹配",
            "test_point": "液体乳应归属于乳制品类别"
        },
        {
            "name": "其他加工产品 - 测试原料来源匹配",
            "data": {
                "品名": "巧克力",
                "原配料": "可可、糖、牛奶",
                "存储条件": "常温保存",
                "保质期": "12个月",
                "净含量": "100g",
                "食用方法": "直接食用",
                "生产加工工艺": "混合制作",
                "食品分类号": "05.01.02",
                "食品分类名称": "巧克力制品",
                "食品原产地": "瑞士"
            },
            "expected_strategy": "原料来源匹配",
            "test_point": "基于可可原料进行匹配"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*80}")
        print(f"测试 {i}: {test_case['name']}")
        print(f"{'='*80}")
        
        print(f"📤 食品分类名称: {test_case['data']['食品分类名称']}")
        print(f"📍 原产地: {test_case['data']['食品原产地']}")
        print(f"🎯 预期匹配策略: {test_case['expected_strategy']}")
        print(f"🔍 测试要点: {test_case['test_point']}")
        
        try:
            response = requests.post(
                "http://localhost:8000/check-food-entry/",
                json=test_case['data'],
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"\n🎯 最终结果: {result.get('准入状态')} - {result.get('原因')}")
                
                # 查找大模型分析结果
                llm_analysis_found = False
                strategy_analysis = None
                
                if "详细信息" in result and "检查过程" in result["详细信息"]:
                    检查过程 = result["详细信息"]["检查过程"]
                    
                    for 步骤名, 步骤信息 in 检查过程.items():
                        if isinstance(步骤信息, dict) and "详情" in 步骤信息:
                            if "大模型完整分类匹配结果" in 步骤信息["详情"]:
                                llm_analysis_found = True
                                大模型结果 = 步骤信息["详情"]["大模型完整分类匹配结果"]
                                strategy_analysis = analyze_matching_strategy(大模型结果)
                                
                                print(f"\n🤖 {步骤名} - 大模型分析:")
                                print(f"  是否允许: {大模型结果.get('is_allowed', 'N/A')}")
                                print(f"  匹配产品: {大模型结果.get('matched_product', 'N/A')}")
                                print(f"  置信度: {大模型结果.get('confidence', 'N/A')}")
                                
                                # 显示匹配策略分析
                                print(f"\n📊 匹配策略分析:")
                                print(f"  通用策略词汇: {strategy_analysis['universal_mentions']} 次")
                                print(f"  具体匹配词汇: {strategy_analysis['specific_mentions']} 次")
                                print(f"  策略类型: {strategy_analysis['analysis']}")
                                print(f"  体现通用性: {'✅ 是' if strategy_analysis['universal_approach'] else '❌ 否'}")
                                
                                # 显示部分详细分析
                                detailed_analysis = 大模型结果.get('detailed_analysis', [])
                                if detailed_analysis:
                                    print(f"\n📋 详细分析示例 (前2项):")
                                    for j, item in enumerate(detailed_analysis[:2], 1):
                                        print(f"  [{j}] {item.get('allowed_product', 'N/A')}")
                                        print(f"      匹配分数: {item.get('match_score', 'N/A')}")
                                        print(f"      分析理由: {item.get('match_reason', 'N/A')[:100]}...")
                                        print(f"      是否匹配: {'✅' if item.get('is_match') else '❌'}")
                
                # 整体评估
                print(f"\n📈 通用匹配策略评估:")
                print(f"  大模型分析执行: {'✅ 成功' if llm_analysis_found else '❌ 失败'}")
                
                if strategy_analysis:
                    universal_score = 1 if strategy_analysis['universal_approach'] else 0
                    balance_score = 1 if strategy_analysis['universal_mentions'] >= strategy_analysis['specific_mentions'] else 0
                    result_score = 1 if result.get('准入状态') == '准入' else 0
                    
                    total_score = universal_score + balance_score + result_score
                    print(f"  体现通用策略: {'✅' if universal_score else '❌'}")
                    print(f"  策略权重平衡: {'✅' if balance_score else '❌'}")
                    print(f"  匹配结果合理: {'✅' if result_score else '❌'}")
                    print(f"  整体评估: {'✅ 优秀' if total_score >= 3 else '⚠️ 良好' if total_score >= 2 else '❌ 需改进'} ({total_score}/3)")
                else:
                    print(f"  无法评估策略通用性")
                
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(response.text)
                
        except Exception as e:
            print(f"💥 错误: {str(e)}")

def main():
    """主函数"""
    print("🧪 测试通用匹配策略的效果")
    
    # 检查服务器
    try:
        response = requests.get("http://localhost:8000/health", timeout=3)
        if response.status_code == 200:
            print("✅ 服务器正常")
        else:
            print("⚠️ 服务器异常")
            return
    except:
        print("❌ 无法连接服务器")
        return
    
    test_universal_matching()
    
    print(f"\n🎉 测试完成!")
    print(f"\n💡 通用匹配策略说明:")
    print(f"  - 实现了工艺等效匹配：蒸煮≈熟制，发酵≈酿造等")
    print(f"  - 实现了类别层次匹配：火腿∈肉制品，牛奶∈乳制品等")
    print(f"  - 实现了形态状态匹配：液体、固体、冷冻等")
    print(f"  - 实现了原料来源匹配：畜肉、禽肉、乳类等")
    print(f"  - 提高了匹配策略的通用性和适应性")

if __name__ == "__main__":
    main()
