from openai import OpenAI
import json
import os
from typing import List, Dict, Any


class LLMService:
    def __init__(self, api_key: str, base_url: str = None, model: str = "Qwen3-32B"):
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url
        )
        self.model = model
    
    async def check_ingredient_in_prohibited_items(self, ingredients: List[str], prohibited_items: List[str]) -> Dict[str, Any]:
        """调用大模型判断配料是否属于禁止进境物"""
        # 输入验证
        if not ingredients or not isinstance(ingredients, list):
            return {"has_prohibited": False, "matched_items": []}

        if not prohibited_items or not isinstance(prohibited_items, list):
            return {"has_prohibited": False, "matched_items": []}

        # 过滤空值和非字符串值
        valid_ingredients = [str(item).strip() for item in ingredients if item and str(item).strip()]
        valid_prohibited = [str(item).strip() for item in prohibited_items if item and str(item).strip()]

        if not valid_ingredients or not valid_prohibited:
            return {"has_prohibited": False, "matched_items": []}

        # 构建类聊天窗口的完整对话结构
        messages = [
            {
                "role": "system",
                "content": (
                    "你是一个专业的食品安全检查专家，严格按照以下规则工作：\n"
                    "1. 识别动物源性配料：肉、皮、骨、未经熟制的动物油脂、乳、蛋\n"
                    "2. 精确动物来源匹配：牛→牛，猪→猪，羊→羊，鸡→鸡/禽\n"
                    "3. 严格避免跨动物匹配\n"
                    "4. 输出必须为严格JSON格式"
                )
            },
            {
                "role": "user",
                "content": (
                    f"### 任务：分析配料是否包含禁止进境物\n\n"
                    "### 配料列表：\n"
                    + "\n".join([f"- {ingredient}" for ingredient in valid_ingredients]) + "\n\n"
                    "### 禁止进境物列表：\n"
                    + "\n".join([f"- {item}" for item in valid_prohibited]) + "\n\n"
                    "### 分析步骤：\n"
                    "1. 识别每个配料的动物来源和成分类型\n"
                    "2. 在禁止列表中查找相同动物来源的禁止物\n"
                    "3. 进行精确匹配或动物来源匹配\n"
                    "4. 输出JSON结果\n\n"
                    "### 输出格式（必须严格遵守）：\n"
                    "{\n"
                    '  "has_prohibited": true/false,\n'
                    '  "matched_items": [\n'
                    '    {\n'
                    '      "ingredient": "配料名称",\n'
                    '      "category": "成分类型",\n'
                    '      "prohibited_item": "匹配的禁止物",\n'
                    '      "reason": "匹配原因"\n'
                    '    }\n'
                    '  ]\n'
                    "}"
                )
            }
        ]

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.1,
                max_tokens=1500,
                top_p=0.95,
                response_format={"type": "json_object"}
            )

            content = response.choices[0].message.content
            # 尝试解析JSON，如果失败则使用回退方案
            try:
                result = json.loads(content)
                # 验证返回结果的格式
                if not isinstance(result, dict):
                    return self._fallback_ingredient_check(valid_ingredients, valid_prohibited)

                if "has_prohibited" not in result:
                    result["has_prohibited"] = False

                if "matched_items" not in result:
                    result["matched_items"] = []

                # 添加分析方法标识
                result["analysis_method"] = "大模型分析"

                return result
            except json.JSONDecodeError:
                return self._fallback_ingredient_check(valid_ingredients, valid_prohibited)

        except Exception:
            return self._fallback_ingredient_check(valid_ingredients, valid_prohibited)
    
    async def check_food_category_match(self, food_category: str, allowed_products: List[str], mapping_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """调用大模型判断食品分类名称是否属于允许的产品"""
        # 输入验证
        if not food_category or not isinstance(food_category, str):
            return {"is_allowed": False, "matched_product": None, "confidence": 0.0, "reason": "食品分类名称无效"}
        
        if not allowed_products or not isinstance(allowed_products, list):
            return {"is_allowed": False, "matched_product": None, "confidence": 0.0, "reason": "允许产品列表为空"}
        
        # 准备数据
        valid_products = [str(p).strip() for p in allowed_products if p and str(p).strip()]
        if not valid_products:
            return {"is_allowed": False, "matched_product": None, "confidence": 0.0, "reason": "没有有效的允许产品"}
        
        food_category = str(food_category).strip()
        
        # 关键改进：构建类聊天窗口的完整对话结构
        messages = [
            {
                "role": "system",
                "content": (
                    "你是一个专业的食品分类专家，严格按照以下规则工作：\n"
                    "1. 基于特征语义匹配，非字面匹配\n"
                    "2. 关键特征提取优先级：加工工艺 > 产品类型 > 原料特征\n"
                    "3. 工艺等价：蒸煮=熟制，熏烤=熟制，腌制=盐渍\n"
                    "4. 类型等价：西式火腿=熟制火腿\n"
                    "5. 输出必须为严格JSON格式"
                )
            },
            {
                "role": "user",
                "content": (
                    f"### 任务：判断「{food_category}」是否属于以下允许产品列表\n\n"
                    "### 食品分类背景信息（核心依据）：\n"
                    f"- 食品类别: {mapping_info.get('食品类别', '') if mapping_info else ''}\n"
                    f"- 分类说明: {mapping_info.get('说明', '') if mapping_info else ''}\n"
                    f"- 主要特征: {mapping_info.get('主要特征', '') if mapping_info else ''}\n\n"
                    "### 允许产品列表：\n"
                    + "\n".join([f"{i+1}. {p}" for i, p in enumerate(valid_products)]) + "\n\n"
                    "### 分析步骤：\n"
                    "1. 从背景信息提取关键特征：\n"
                    "   - 工艺特征（如：蒸煮、熟制、腌制）\n"
                    "   - 产品类型（如：火腿、香肠）\n"
                    "   - 原料特征（如：猪肉、禽肉）\n"
                    "2. 将提取特征与产品列表进行语义匹配\n"
                    "3. 特别注意工艺等价关系（蒸煮=熟制）\n"
                    "4. 输出JSON结果\n\n"
                    "### 输出格式（必须严格遵守）：\n"
                    "{\n"
                    '  "is_allowed": true/false,\n'
                    '  "matched_product": "最佳匹配名称",\n'
                    '  "confidence": 0.0-1.0,\n'
                    '  "reason": "匹配依据",\n'
                    '  "detailed_analysis": [\n'
                    '    {"product": "名称", "score": 0.0-1.0, "reason":"分析", "is_match": true/false}\n'
                    "  ]\n"
                    "}"
                )
            }
        ]
        
        # 关键参数：使用与聊天窗口相同的配置
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.1,       # 低随机性确保一致性
                max_tokens=1500,       # 增加token限额
                top_p=0.95,            # 与聊天窗口默认值一致
                response_format={"type": "json_object"}  # 强制JSON输出
            )
            
            content = response.choices[0].message.content
            
            # 增强型JSON解析
            try:
                # 处理可能的JSON包装
                if content.startswith("```json"):
                    content = content[7:-3].strip()
                
                result = json.loads(content)
                
                # 验证必要字段
                required_keys = ["is_allowed", "matched_product", "confidence", "reason"]
                if not all(key in result for key in required_keys):
                    raise ValueError("缺少必要字段")
                    
                # 标准化置信度
                result["confidence"] = max(0.0, min(1.0, float(result.get("confidence", 0.5))))
                
                # 添加分析元数据
                result["analysis_method"] = "增强大模型分析"
                result["input_category"] = food_category
                return result
                
            except (json.JSONDecodeError, ValueError):
                # JSON解析失败，使用备选方案
                return self._fallback_category_check(food_category, valid_products)

        except Exception:
            # API调用失败，使用备选方案
            return self._fallback_category_check(food_category, valid_products)
    
    async def extract_dairy_ingredients(self, ingredients: List[str]) -> List[str]:
        """调用大模型提取配料中与乳相关的配料"""
        # 输入验证
        if not ingredients or not isinstance(ingredients, list):
            return []

        # 过滤空值和非字符串值
        valid_ingredients = [str(item).strip() for item in ingredients if item and str(item).strip()]
        if not valid_ingredients:
            return []

        # 构建类聊天窗口的完整对话结构
        messages = [
            {
                "role": "system",
                "content": (
                    "你是一个专业的食品配料分析专家，严格按照以下规则工作：\n"
                    "1. 识别所有乳制品相关配料\n"
                    "2. 包括：牛奶、羊奶、奶粉、乳清、酪蛋白、乳糖、奶油、黄油、奶酪等\n"
                    "3. 考虑各种乳制品的变体和加工形式\n"
                    "4. 输出必须为严格JSON格式"
                )
            },
            {
                "role": "user",
                "content": (
                    f"### 任务：从配料列表中提取所有乳制品相关配料\n\n"
                    "### 配料列表：\n"
                    + "\n".join([f"- {ingredient}" for ingredient in valid_ingredients]) + "\n\n"
                    "### 分析步骤：\n"
                    "1. 逐个检查每个配料\n"
                    "2. 识别是否含有乳制品成分\n"
                    "3. 提取所有乳相关配料\n"
                    "4. 输出JSON结果\n\n"
                    "### 输出格式（必须严格遵守）：\n"
                    "{\n"
                    '  "dairy_ingredients": ["乳相关配料1", "乳相关配料2"]\n'
                    "}"
                )
            }
        ]

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.1,
                max_tokens=800,
                top_p=0.95,
                response_format={"type": "json_object"}
            )

            content = response.choices[0].message.content
            try:
                result = json.loads(content)
                dairy_ingredients = result.get("dairy_ingredients", [])
                # 确保返回的是字符串列表
                if isinstance(dairy_ingredients, list):
                    extracted_ingredients = [str(item).strip() for item in dairy_ingredients if item and str(item).strip()]
                    # 注意：这里返回的是列表，但我们需要在调用处记录分析方法
                    return extracted_ingredients
                else:
                    return self._fallback_dairy_extraction(valid_ingredients)
            except json.JSONDecodeError:
                return self._fallback_dairy_extraction(valid_ingredients)

        except Exception:
            return self._fallback_dairy_extraction(valid_ingredients)

    async def extract_aquatic_ingredients(self, ingredients: List[str]) -> List[str]:
        """调用大模型提取配料中与水产品相关的配料"""
        if not ingredients:
            return []

        valid_ingredients = [str(item).strip() for item in ingredients if item and str(item).strip()]
        if not valid_ingredients:
            return []

        # 构建类聊天窗口的完整对话结构
        messages = [
            {
                "role": "system",
                "content": (
                    "你是一个专业的水产品分类专家，严格按照以下规则工作：\n"
                    "1. 识别所有水产品相关配料，特别关注拉丁文学名\n"
                    "2. 包括：鱼类、虾类、蟹类、贝类、海藻类、鱼油、鱼粉等\n"
                    "3. 优先识别和保留拉丁文学名信息\n"
                    "4. 输出必须为严格JSON格式"
                )
            },
            {
                "role": "user",
                "content": (
                    f"### 任务：从配料列表中提取所有水产品相关配料，特别关注拉丁文学名\n\n"
                    "### 配料列表：\n"
                    + "\n".join([f"- {ingredient}" for ingredient in valid_ingredients]) + "\n\n"
                    "### 分析要求：\n"
                    "1. 识别所有水产品相关配料\n"
                    "2. 特别关注和提取拉丁文学名（如 Salmo salar, Gadus morhua 等）\n"
                    "3. 如果配料中包含拉丁文学名，优先保留完整的拉丁文学名\n"
                    "4. 输出JSON结果\n\n"
                    "### 输出格式（必须严格遵守）：\n"
                    "{\n"
                    '  "aquatic_ingredients": [\n'
                    '    {\n'
                    '      "ingredient": "原始配料名称",\n'
                    '      "latin_name": "拉丁文学名（如有）",\n'
                    '      "common_name": "中文名称（如有）"\n'
                    '    }\n'
                    '  ]\n'
                    "}"
                )
            }
        ]

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.1,
                max_tokens=800,
                top_p=0.95,
                response_format={"type": "json_object"}
            )

            content = response.choices[0].message.content
            try:
                result = json.loads(content)
                aquatic_ingredients = result.get("aquatic_ingredients", [])

                # 处理新的结构化返回格式
                if aquatic_ingredients and isinstance(aquatic_ingredients[0], dict):
                    return aquatic_ingredients
                else:
                    # 兼容旧格式，转换为新格式
                    return [{"ingredient": ing, "latin_name": "", "common_name": ing}
                           for ing in aquatic_ingredients if ing]
            except json.JSONDecodeError:
                return self._fallback_aquatic_extraction(valid_ingredients)

        except Exception:
            return self._fallback_aquatic_extraction(valid_ingredients)

    def _fallback_aquatic_extraction(self, ingredients: List[str]) -> List[Dict[str, str]]:
        """备选方案：基于关键词的水产品配料提取"""
        aquatic_keywords = [
            '鱼', '虾', '蟹', '贝', '蛤', '蚌', '螺', '章鱼', '鱿鱼', '海参',
            '海带', '紫菜', '海苔', '鱼油', '鱼粉', '虾粉', '蟹粉', '海鲜',
            '三文鱼', '金枪鱼', '鲑鱼', '鳕鱼', '带鱼', '黄鱼', '鲈鱼'
        ]

        aquatic_ingredients = []
        for ingredient in ingredients:
            ingredient_str = str(ingredient).lower()
            if any(keyword in ingredient_str for keyword in aquatic_keywords):
                aquatic_ingredients.append({
                    "ingredient": ingredient,
                    "latin_name": "",  # 备选方案无法提取拉丁文学名
                    "common_name": ingredient
                })

        return aquatic_ingredients

    async def check_aquatic_ingredients_match(self, aquatic_ingredients: List[Dict[str, str]], allowed_products: List[str], storage_condition: str = "") -> Dict[str, Any]:
        """调用大模型判断水产品配料是否属于允许产品列表，特别关注拉丁文学名匹配"""
        if not aquatic_ingredients or not allowed_products:
            return {"is_allowed": False, "reason": "输入参数无效"}

        valid_products = [str(p).strip() for p in allowed_products if p and str(p).strip()]
        if not valid_products:
            return {"is_allowed": False, "reason": "允许产品列表为空"}

        # 构建配料信息字符串
        ingredients_info = []
        for ing in aquatic_ingredients:
            info_parts = [f"配料: {ing.get('ingredient', '')}"]
            if ing.get('latin_name'):
                info_parts.append(f"拉丁文学名: {ing.get('latin_name')}")
            if ing.get('common_name'):
                info_parts.append(f"中文名: {ing.get('common_name')}")
            ingredients_info.append(" | ".join(info_parts))

        # 构建类聊天窗口的完整对话结构
        messages = [
            {
                "role": "system",
                "content": (
                    "你是一个专业的水产品分类匹配专家，严格按照以下规则工作：\n"
                    "1. 优先使用拉丁文学名进行匹配，拉丁文学名是最准确的分类依据\n"
                    "2. 拉丁文学名匹配优先级高于中文名称匹配\n"
                    "3. 同时判断存储条件是否符合产品要求\n"
                    "4. 输出必须为严格JSON格式"
                )
            },
            {
                "role": "user",
                "content": (
                    f"### 任务：判断水产品配料是否属于允许产品列表，并检查存储条件\n\n"
                    "### 水产品配料信息：\n"
                    + "\n".join([f"- {info}" for info in ingredients_info]) + "\n\n"
                    "### 允许产品列表：\n"
                    + "\n".join([f"- {product}" for product in valid_products]) + "\n\n"
                    f"### 实际存储条件：{storage_condition}\n\n"
                    "### 匹配和存储条件判断规则：\n"
                    "1. **产品匹配**：\n"
                    "   - 优先使用拉丁文学名匹配\n"
                    "   - 拉丁文学名是种属分类的标准，同一拉丁文学名表示同一物种\n"
                    "   - 如果没有拉丁文学名，再考虑中文名称的语义匹配\n"
                    "2. **存储条件判断**：\n"
                    "   - 如果匹配的产品名称包含[冷冻等]标记，则要求实际存储条件必须为冷冻\n"
                    "   - 冰鲜/冷藏不等于冷冻，只有明确包含'冷冻'的存储条件才符合冷冻要求\n"
                    "   - 如果产品名称未标明存储条件，则鲜水产和冷冻水产品均可\n"
                    "3. **综合判断**：只有产品匹配成功且存储条件符合才返回允许\n\n"
                    "### 输出格式（必须严格遵守）：\n"
                    "{\n"
                    '  "is_allowed": true/false,\n'
                    '  "matched_product": "匹配的产品名称",\n'
                    '  "match_type": "latin_name/common_name",\n'
                    '  "storage_check": "pass/fail/not_required",\n'
                    '  "storage_reason": "存储条件检查说明",\n'
                    '  "confidence": 0.0-1.0,\n'
                    '  "reason": "综合判断理由"\n'
                    "}"
                )
            }
        ]

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.1,
                max_tokens=800,
                top_p=0.95,
                response_format={"type": "json_object"}
            )

            content = response.choices[0].message.content
            try:
                result = json.loads(content)
                return result
            except json.JSONDecodeError:
                return self._fallback_aquatic_match(aquatic_ingredients, valid_products, storage_condition)

        except Exception:
            return self._fallback_aquatic_match(aquatic_ingredients, valid_products, storage_condition)

    def _fallback_aquatic_match(self, aquatic_ingredients: List[Dict[str, str]], allowed_products: List[str], storage_condition: str = "") -> Dict[str, Any]:
        """备选方案：水产品配料匹配和存储条件检查"""
        for ing in aquatic_ingredients:
            ingredient_name = ing.get('ingredient', '')
            latin_name = ing.get('latin_name', '')
            common_name = ing.get('common_name', '')

            for product in allowed_products:
                product_lower = str(product).lower()
                matched = False
                match_type = ""
                match_reason = ""

                # 优先检查拉丁文学名匹配
                if latin_name and latin_name.lower() in product_lower:
                    matched = True
                    match_type = "latin_name"
                    match_reason = f"拉丁文学名匹配：{latin_name}"
                # 其次检查中文名称匹配
                elif common_name and (common_name.lower() in product_lower or product_lower in common_name.lower()):
                    matched = True
                    match_type = "common_name"
                    match_reason = f"中文名称匹配：{common_name}"

                if matched:
                    # 检查存储条件
                    if '[冷冻]' in product:
                        # 产品要求冷冻
                        if storage_condition and '冷冻' in storage_condition:
                            return {
                                "is_allowed": True,
                                "matched_product": product,
                                "match_type": match_type,
                                "storage_check": "pass",
                                "storage_reason": "存储条件符合冷冻要求",
                                "confidence": 0.8,
                                "reason": f"{match_reason}，存储条件检查通过",
                                "analysis_method": "备选方案"
                            }
                        else:
                            return {
                                "is_allowed": False,
                                "matched_product": product,
                                "match_type": match_type,
                                "storage_check": "fail",
                                "storage_reason": f"产品要求冷冻，但实际存储条件为{storage_condition}（冰鲜/冷藏≠冷冻）",
                                "confidence": 0.9,
                                "reason": f"{match_reason}，但存储条件不符合",
                                "analysis_method": "备选方案"
                            }
                    else:
                        # 产品无特殊存储要求
                        return {
                            "is_allowed": True,
                            "matched_product": product,
                            "match_type": match_type,
                            "storage_check": "not_required",
                            "storage_reason": "产品无特殊存储条件要求",
                            "confidence": 0.8,
                            "reason": f"{match_reason}，无存储条件限制",
                            "analysis_method": "备选方案"
                        }

        return {
            "is_allowed": False,
            "matched_product": None,
            "match_type": "none",
            "storage_check": "not_applicable",
            "storage_reason": "未找到匹配产品",
            "confidence": 0.9,
            "reason": "未找到匹配的水产品",
            "analysis_method": "备选方案"
        }

    async def check_ingredient_in_allowed_products(self, ingredient: str, allowed_products: List[str]) -> Dict[str, Any]:
        """调用大模型判断配料是否在允许产品列表中"""
        if not ingredient or not allowed_products:
            return {"is_allowed": False, "reason": "输入参数无效"}

        ingredient = str(ingredient).strip()
        valid_products = [str(p).strip() for p in allowed_products if p and str(p).strip()]

        if not valid_products:
            return {"is_allowed": False, "reason": "允许产品列表为空"}

        # 构建类聊天窗口的完整对话结构
        messages = [
            {
                "role": "system",
                "content": (
                    "你是一个专业的食品配料匹配专家，严格按照以下规则工作：\n"
                    "1. 判断配料是否属于允许产品列表中的任何一种\n"
                    "2. 基于语义匹配，考虑同义词和相关性\n"
                    "3. 考虑配料的不同表达方式和加工形式\n"
                    "4. 输出必须为严格JSON格式"
                )
            },
            {
                "role": "user",
                "content": (
                    f"### 任务：判断配料「{ingredient}」是否属于以下允许产品列表\n\n"
                    "### 允许产品列表：\n"
                    + "\n".join([f"- {product}" for product in valid_products]) + "\n\n"
                    "### 分析步骤：\n"
                    "1. 理解配料的含义和特征\n"
                    "2. 在允许产品列表中寻找匹配项\n"
                    "3. 考虑语义相似性和相关性\n"
                    "4. 输出JSON结果\n\n"
                    "### 输出格式（必须严格遵守）：\n"
                    "{\n"
                    '  "is_allowed": true/false,\n'
                    '  "matched_product": "匹配的产品名称",\n'
                    '  "confidence": 0.0-1.0,\n'
                    '  "reason": "匹配理由"\n'
                    "}"
                )
            }
        ]

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.1,
                max_tokens=800,
                top_p=0.95,
                response_format={"type": "json_object"}
            )

            content = response.choices[0].message.content
            try:
                result = json.loads(content)
                return result
            except json.JSONDecodeError:
                return self._fallback_ingredient_match(ingredient, valid_products)

        except Exception:
            return self._fallback_ingredient_match(ingredient, valid_products)

    def _fallback_ingredient_match(self, ingredient: str, allowed_products: List[str]) -> Dict[str, Any]:
        """备选方案：简单的配料匹配检查"""
        ingredient_lower = ingredient.lower()

        for product in allowed_products:
            product_lower = str(product).lower()
            # 双向包含匹配
            if ingredient_lower in product_lower or product_lower in ingredient_lower:
                return {
                    "is_allowed": True,
                    "matched_product": product,
                    "confidence": 0.8,
                    "reason": f"字符串匹配：{ingredient} 与 {product}",
                    "analysis_method": "备选方案"
                }

        return {
            "is_allowed": False,
            "matched_product": None,
            "confidence": 0.9,
            "reason": f"未找到匹配的产品：{ingredient}",
            "analysis_method": "备选方案"
        }

    async def check_remark_compliance(self, dairy_ingredients: List[str], remark: str) -> Dict[str, Any]:
        """调用大模型判断乳制品配料是否满足备注要求"""
        if not dairy_ingredients or not remark:
            return {"compliant": True, "reason": "无需检查"}

        valid_ingredients = [str(item).strip() for item in dairy_ingredients if item and str(item).strip()]
        if not valid_ingredients:
            return {"compliant": True, "reason": "无乳制品配料"}

        # 构建类聊天窗口的完整对话结构
        messages = [
            {
                "role": "system",
                "content": (
                    "你是一个专业的乳制品合规分析专家，严格按照以下规则工作：\n"
                    "1. 仔细理解备注中的具体要求和限制条件\n"
                    "2. 分析乳制品配料是否符合备注要求\n"
                    "3. 考虑年龄限制、用途限制、成分限制等\n"
                    "4. 输出必须为严格JSON格式"
                )
            },
            {
                "role": "user",
                "content": (
                    f"### 任务：判断乳制品配料是否满足备注要求\n\n"
                    "### 乳相关配料：\n"
                    + "\n".join([f"- {ingredient}" for ingredient in valid_ingredients]) + "\n\n"
                    f"### 备注要求：\n{remark}\n\n"
                    "### 分析步骤：\n"
                    "1. 理解备注中的具体要求和限制\n"
                    "2. 检查每个乳制品配料是否符合要求\n"
                    "3. 判断是否存在违反备注要求的情况\n"
                    "4. 输出JSON结果\n\n"
                    "### 输出格式（必须严格遵守）：\n"
                    "{\n"
                    '  "compliant": true/false,\n'
                    '  "reason": "详细的判断理由"\n'
                    "}"
                )
            }
        ]

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.1,
                max_tokens=800,
                top_p=0.95,
                response_format={"type": "json_object"}
            )

            content = response.choices[0].message.content
            try:
                result = json.loads(content)
                return result
            except json.JSONDecodeError:
                return self._fallback_remark_check(valid_ingredients, remark)

        except Exception:
            return self._fallback_remark_check(valid_ingredients, remark)

    def _fallback_remark_check(self, dairy_ingredients: List[str], remark: str) -> Dict[str, Any]:
        """备选方案：简单的备注要求检查"""
        # 基本的关键词检查
        remark_lower = remark.lower()

        # 常见的限制性关键词
        restrictive_keywords = ['不得', '禁止', '不允许', '不能', '限制', '除外', '不可']
        permissive_keywords = ['允许', '可以', '准许', '符合', '可用']

        # 如果备注中包含限制性关键词，默认不通过
        if any(keyword in remark_lower for keyword in restrictive_keywords):
            return {
                "compliant": False,
                "reason": f"不满足备注要求：{remark}",
                "analysis_method": "备选方案"
            }

        # 如果备注中包含允许性关键词，默认通过
        if any(keyword in remark_lower for keyword in permissive_keywords):
            return {
                "compliant": True,
                "reason": f"满足备注要求：{remark}",
                "analysis_method": "备选方案"
            }

        # 其他情况，按照逻辑应该是不满足要求（保守处理）
        return {
            "compliant": False,
            "reason": f"不满足备注要求：{remark}",
            "analysis_method": "备选方案"
        }

    def _fallback_ingredient_check(self, ingredients: List[str], prohibited_items: List[str]) -> Dict[str, Any]:
        """改进的字符串匹配备选方案"""
        all_items = []

        # 动物关键词映射（严格区分）
        animal_keywords = {
            '牛': ['牛', 'beef', 'cattle'],
            '猪': ['猪', 'pork', 'pig', 'swine'],
            '羊': ['羊', 'sheep', 'lamb', 'mutton'],
            '鸡': ['鸡', 'chicken'],
            '禽': ['禽', 'poultry', 'bird'],
            '鸭': ['鸭', 'duck'],
            '鹅': ['鹅', 'goose']
        }

        # 成分关键词
        component_keywords = ['肉', '皮', '骨', '油', '脂', '乳', '奶', '蛋']

        for ingredient in ingredients:
            if not ingredient:
                continue

            ingredient_str = str(ingredient).strip()

            # 检测动物来源
            detected_animal = None
            for animal, keywords in animal_keywords.items():
                if any(keyword in ingredient_str for keyword in keywords):
                    detected_animal = animal
                    break

            # 检测成分类型
            detected_component = None
            for component in component_keywords:
                if component in ingredient_str:
                    detected_component = component
                    break

            # 检查是否匹配禁止物
            matched_prohibited = None
            match_reason = "未匹配到禁止物"

            for prohibited in prohibited_items:
                if not prohibited:
                    continue

                prohibited_str = str(prohibited).strip()

                # 严格匹配逻辑（避免跨动物匹配）
                match_found = False

                # 1. 直接字符串匹配
                if ingredient_str in prohibited_str or prohibited_str in ingredient_str:
                    match_found = True
                    match_reason = f"直接匹配: '{ingredient_str}' 包含在禁止物 '{prohibited_str}' 中"

                # 2. 动物来源严格匹配（确保同一动物）
                elif detected_animal:
                    # 检查禁止物是否包含相同的动物关键词
                    animal_keywords_for_detected = animal_keywords.get(detected_animal, [])
                    if any(keyword in prohibited_str for keyword in animal_keywords_for_detected):
                        # 进一步验证：确保不是其他动物的关键词
                        is_cross_match = False
                        for other_animal, other_keywords in animal_keywords.items():
                            if other_animal != detected_animal:
                                if any(other_keyword in prohibited_str for other_keyword in other_keywords):
                                    # 如果禁止物同时包含其他动物关键词，需要更精确判断
                                    if not any(keyword in prohibited_str for keyword in animal_keywords_for_detected):
                                        is_cross_match = True
                                        break

                        if not is_cross_match:
                            match_found = True
                            match_reason = f"动物来源匹配: '{ingredient_str}' 的动物来源 '{detected_animal}' 与禁止物 '{prohibited_str}' 匹配"

                # 3. 成分类型匹配（结合动物来源验证）
                elif detected_component:
                    if detected_component in prohibited_str:
                        # 如果有动物来源，必须确保动物匹配
                        if detected_animal:
                            animal_keywords_for_detected = animal_keywords.get(detected_animal, [])
                            if any(keyword in prohibited_str for keyword in animal_keywords_for_detected):
                                match_found = True
                                match_reason = f"成分+动物匹配: '{ingredient_str}' 的 '{detected_component}' + '{detected_animal}' 与禁止物 '{prohibited_str}' 匹配"
                        else:
                            match_found = True
                            match_reason = f"成分匹配: '{ingredient_str}' 的 '{detected_component}' 与禁止物 '{prohibited_str}' 匹配"

                if match_found:
                    matched_prohibited = prohibited_str
                    break

            all_items.append({
                "ingredient": ingredient_str,
                "category": detected_component or "其他",
                "prohibited_item": matched_prohibited or "无",
                "reason": match_reason,
                "animal_source": detected_animal or "未识别"
            })

        return {
            "has_prohibited": any(item.get("prohibited_item") != "无" for item in all_items),
            "matched_items": all_items,
            "analysis_method": "改进字符串匹配（备选方案）"
        }
    
    async def _repair_json_response(self, raw_content: str, food_category: str, products: list) -> dict:
        """JSON解析失败时进行修复"""
        repair_prompt = f"""
        原始问题：判断「{food_category}」的匹配结果
        模型返回的错误内容：{raw_content[:1000]}
        
        请修正为以下JSON格式：
        {{
            "is_allowed": ...,
            "matched_product": ...,
            "confidence": ...,
            "reason": ...,
            "detailed_analysis": [...]
        }}
        """
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": repair_prompt}],
                temperature=0.0,  # 零随机性确保准确
                response_format={"type": "json_object"}
            )
            return json.loads(response.choices[0].message.content)
        except:
            return self._fallback_analysis(food_category, products)

    def _fallback_analysis(self, food_category: str, products: list, mapping_info: dict = None) -> dict:
        """肉制品后备分析方案"""
        # 从映射信息中提取关键词
        key_terms = set()
        if mapping_info:
            for field in ['说明', '主要特征', '食品类别']:
                text = mapping_info.get(field, '')
                if text:
                    # 提取2-4个字符的中文词汇
                    import re
                    words = re.findall(r'[\u4e00-\u9fff]{2,4}', text)
                    key_terms.update(words)

        # 如果没有关键词，从食品分类名称中提取
        if not key_terms:
            import re
            words = re.findall(r'[\u4e00-\u9fff]{2,4}', food_category)
            key_terms.update(words)

        # 在允许产品中寻找匹配项
        matched = []
        for product in products:
            product_str = str(product)
            match_count = sum(1 for term in key_terms if term in product_str)
            if match_count > 0:
                matched.append({
                    "product": product,
                    "match_count": match_count,
                    "score": min(0.9, match_count / len(key_terms) + 0.3) if key_terms else 0.5
                })

        # 按匹配数量排序
        matched.sort(key=lambda x: x["match_count"], reverse=True)

        is_allowed = len(matched) > 0
        best_match = matched[0] if matched else None

        return {
            "is_allowed": is_allowed,
            "matched_product": best_match["product"] if best_match else None,
            "confidence": best_match["score"] if best_match else 0.1,
            "reason": f"通用匹配：提取{len(key_terms)}个关键词，找到{len(matched)}个匹配产品",
            "analysis_method": "后备分析"
        }
    

    def _fallback_category_check(self, food_category: str, allowed_products: List[str]) -> Dict[str, Any]:
        """简单字符串匹配的回退方案"""
        for product in allowed_products:
            if food_category in product or product in food_category:
                return {
                    "is_allowed": True,
                    "matched_product": product,
                    "confidence": 0.8,
                    "reason": "字符串匹配"
                }
        
        return {
            "is_allowed": False,
            "matched_product": None,
            "confidence": 0.9,
            "reason": "未找到匹配的产品"
        }
    
    def _fallback_dairy_extraction(self, ingredients: List[str]) -> List[str]:
        """关键词匹配的回退方案"""
        dairy_keywords = ["奶", "乳", "牛奶", "羊奶", "奶粉", "乳清", "酪蛋白", "乳糖", "奶油", "黄油", "奶酪", "芝士"]
        dairy_ingredients = []
        
        for ingredient in ingredients:
            for keyword in dairy_keywords:
                if keyword in ingredient:
                    dairy_ingredients.append(ingredient)
                    break
        
        return dairy_ingredients

# 全局LLM服务实例
llm_service = None

def get_llm_service():
    global llm_service
    if llm_service is None:
        try:
            # 使用您配置文件中的设置
            from .config import settings

            llm_service = LLMService(
                api_key=settings.openai_api_key,
                base_url=settings.openai_base_url,
                model=settings.llm_model
            )
        except Exception:
            # 返回一个模拟的LLM服务，只使用备选方案
            llm_service = MockLLMService()
    return llm_service








class MockLLMService:
    """模拟LLM服务，当真实LLM不可用时使用"""

    async def check_ingredient_in_prohibited_items(self, ingredients: List[str], prohibited_items: List[str]) -> Dict[str, Any]:
        return self._fallback_ingredient_check(ingredients, prohibited_items)

    async def check_food_category_match(self, food_category: str, allowed_products: List[str], mapping_info: Dict[str, Any] = None) -> Dict[str, Any]:
        return self._fallback_category_check(food_category, allowed_products)

    async def extract_dairy_ingredients(self, ingredients: List[str]) -> List[str]:
        return self._fallback_dairy_extraction(ingredients)

    async def check_remark_compliance(self, dairy_ingredients: List[str], remark: str) -> Dict[str, Any]:
        return {"compliant": True, "reason": "备选方案：默认通过"}

    async def check_ingredient_in_allowed_products(self, ingredient: str, allowed_products: List[str]) -> Dict[str, Any]:
        return self._fallback_ingredient_match(ingredient, allowed_products)

    async def extract_aquatic_ingredients(self, ingredients: List[str]) -> List[Dict[str, str]]:
        return self._fallback_aquatic_extraction(ingredients)

    async def check_aquatic_ingredients_match(self, aquatic_ingredients: List[Dict[str, str]], allowed_products: List[str], storage_condition: str = "") -> Dict[str, Any]:
        return self._fallback_aquatic_match(aquatic_ingredients, allowed_products, storage_condition)

    def _fallback_ingredient_check(self, ingredients: List[str], prohibited_items: List[str]) -> Dict[str, Any]:
        """简单字符串匹配的回退方案"""
        matched_items = []
        for ingredient in ingredients:
            for prohibited in prohibited_items:
                if ingredient in prohibited or prohibited in ingredient:
                    matched_items.append({
                        "ingredient": ingredient,
                        "prohibited_item": prohibited,
                        "reason": "字符串匹配（备选方案）"
                    })

        return {
            "has_prohibited": len(matched_items) > 0,
            "matched_items": matched_items,
            "analysis_method": "字符串匹配（备选方案）"
        }

    def _fallback_category_check(self, food_category: str, allowed_products: List[str]) -> Dict[str, Any]:
        """逐一产品分析的备选方案"""
        food_category_lower = food_category.lower()

        # 对每个允许产品进行详细分析
        detailed_analysis = []
        best_match = None
        best_score = 0.0

        for product in allowed_products:
            product_lower = product.lower()
            match_score = 0.0
            match_reasons = []
            is_match = False

            # 1. 直接字符串匹配
            if food_category_lower in product_lower or product_lower in food_category_lower:
                match_score = 0.9
                match_reasons.append("直接字符串匹配")
                is_match = True

            # 2. 关键词匹配
            elif not is_match:
                food_keywords = [word for word in food_category_lower.replace('(', ' ').replace(')', ' ').replace('，', ' ').replace(',', ' ').split() if len(word) > 1]
                matched_keywords = []
                for keyword in food_keywords:
                    if keyword in product_lower:
                        matched_keywords.append(keyword)

                if len(matched_keywords) >= 1 and len(food_keywords) > 0:
                    keyword_ratio = len(matched_keywords) / len(food_keywords)
                    if keyword_ratio >= 0.3:  # 至少30%的关键词匹配
                        match_score = 0.7 * keyword_ratio
                        match_reasons.append(f"关键词匹配: {matched_keywords}")
                        is_match = True

            # 如果没有匹配，给出分析原因
            if not is_match:
                match_reasons.append("无明显语义关联")

            # 记录分析结果
            detailed_analysis.append({
                "allowed_product": product,
                "match_score": round(match_score, 2),
                "match_reason": "; ".join(match_reasons),
                "is_match": is_match
            })

            # 更新最佳匹配
            if is_match and match_score > best_score:
                best_score = match_score
                best_match = product

        # 返回结果
        return {
            "is_allowed": best_match is not None,
            "matched_product": best_match,
            "confidence": round(best_score, 2),
            "reason": f"逐一分析了 {len(allowed_products)} 个允许产品，{'找到最佳匹配' if best_match else '未找到匹配'}",
            "analysis_method": "逐一产品分析（备选方案）",
            "detailed_analysis": detailed_analysis
        }

    def _fallback_dairy_extraction(self, ingredients: List[str]) -> List[str]:
        """关键词匹配的回退方案"""
        dairy_keywords = ["奶", "乳", "牛奶", "羊奶", "奶粉", "乳清", "酪蛋白", "乳糖", "奶油", "黄油", "奶酪", "芝士"]
        dairy_ingredients = []

        for ingredient in ingredients:
            for keyword in dairy_keywords:
                if keyword in ingredient:
                    dairy_ingredients.append(ingredient)
                    break

        return dairy_ingredients
