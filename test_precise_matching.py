#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试映射类别号7和8的精准匹配功能（不使用大模型）
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal
from app.schemas import FoodCheckRequest
from app.main import check_bird_nest_product

async def test_precise_matching():
    """测试精准匹配功能"""
    db = SessionLocal()
    
    try:
        print("=== 测试映射类别号7（燕窝）精准匹配 ===\n")
        
        # 测试用例1：完全匹配
        print("1. 测试完全匹配")
        request1 = FoodCheckRequest(
            食品原产地="马来西亚",
            食品分类名称="燕窝制品",
            品名="即食燕窝",
            食品配料="燕窝, 冰糖, 水",
            存储条件="常温"
        )
        
        映射信息1 = {"食品类别": "燕窝"}  # 假设数据库中有完全匹配的"燕窝"产品
        
        result1 = await check_bird_nest_product(db, request1, 映射信息1)
        print(f"   食品类别: {映射信息1['食品类别']}")
        print(f"   结果: {'准入' if result1['allowed'] else '不准入'}")
        print(f"   原因: {result1['reason']}")
        print(f"   匹配的产品: {result1['details'].get('匹配的产品', '无')}")
        
        # 测试用例2：包含匹配
        print("\n2. 测试包含匹配")
        request2 = FoodCheckRequest(
            食品原产地="印尼",
            食品分类名称="燕窝制品",
            品名="燕窝饮品",
            食品配料="燕窝, 其他成分",
            存储条件="常温"
        )
        
        映射信息2 = {"食品类别": "即食燕窝"}  # 假设数据库中有"即食燕窝制品"这样的产品
        
        result2 = await check_bird_nest_product(db, request2, 映射信息2)
        print(f"   食品类别: {映射信息2['食品类别']}")
        print(f"   结果: {'准入' if result2['allowed'] else '不准入'}")
        print(f"   原因: {result2['reason']}")
        print(f"   匹配的产品: {result2['details'].get('匹配的产品', '无')}")
        
        # 测试用例3：无匹配
        print("\n3. 测试无匹配情况")
        request3 = FoodCheckRequest(
            食品原产地="泰国",
            食品分类名称="燕窝制品",
            品名="特殊燕窝产品",
            食品配料="燕窝, 特殊成分",
            存储条件="常温"
        )
        
        映射信息3 = {"食品类别": "不存在的产品类别"}
        
        result3 = await check_bird_nest_product(db, request3, 映射信息3)
        print(f"   食品类别: {映射信息3['食品类别']}")
        print(f"   结果: {'准入' if result3['allowed'] else '不准入'}")
        print(f"   原因: {result3['reason']}")
        print(f"   允许的产品列表: {result3['details'].get('允许的产品列表', [])}")
        
        print("\n=== 精准匹配逻辑说明 ===")
        print("✅ 映射类别号7（燕窝）使用精准匹配：")
        print("   1. 完全匹配：食品类别 == 产品名称")
        print("   2. 包含匹配：食品类别 in 产品名称 或 产品名称 in 食品类别")
        print("   3. 不使用大模型，只进行数据库精准匹配")
        print("   4. 匹配失败时返回详细的产品列表供参考")
        
        print("\n✅ 映射类别号8（其他）处理逻辑：")
        print("   1. 直接返回：需人工审核")
        print("   2. 不进行任何数据库查询或匹配")
        print("   3. 不使用大模型")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_precise_matching())
