#!/usr/bin/env python3
"""
测试逐一产品分析功能
"""

import requests
import json

def print_detailed_analysis(analysis_result):
    """打印详细的产品分析结果"""
    if isinstance(analysis_result, dict):
        print(f"    分析方法: {analysis_result.get('analysis_method', 'N/A')}")
        print(f"    是否允许: {analysis_result.get('is_allowed', 'N/A')}")
        print(f"    最佳匹配产品: {analysis_result.get('matched_product', 'N/A')}")
        print(f"    置信度: {analysis_result.get('confidence', 'N/A')}")
        print(f"    总体分析: {analysis_result.get('reason', 'N/A')}")
        
        # 显示逐一产品分析结果
        detailed_analysis = analysis_result.get('detailed_analysis', [])
        if detailed_analysis:
            print(f"    逐一产品分析结果:")
            for i, item in enumerate(detailed_analysis, 1):
                match_status = "✅ 匹配" if item.get('is_match') else "❌ 不匹配"
                print(f"      [{i}] {item.get('allowed_product', 'N/A')}")
                print(f"          匹配状态: {match_status}")
                print(f"          匹配分数: {item.get('match_score', 'N/A')}")
                print(f"          分析原因: {item.get('match_reason', 'N/A')}")
        else:
            print(f"    未找到详细分析结果")
    else:
        print(f"    原始结果: {analysis_result}")

def test_detailed_product_analysis():
    """测试逐一产品分析功能"""
    
    test_cases = [
        {
            "name": "西式火腿 - 测试与多种火腿产品的匹配",
            "data": {
                "品名": "西式火腿",
                "原配料": "猪肉、盐、糖、香辛料",
                "存储条件": "冷藏保存",
                "保质期": "60天",
                "净含量": "500g",
                "食用方法": "切片食用",
                "生产加工工艺": "熏烤制作",
                "食品分类号": "02.01.01",
                "食品分类名称": "西式火腿(熏烤、烟熏、蒸煮火腿)",
                "食品原产地": "德国"
            },
            "expected_matches": ["熟制火腿", "火腿", "腿肉制品"]
        },
        {
            "name": "中式香肠 - 测试与香肠类产品的匹配",
            "data": {
                "品名": "中式香肠",
                "原配料": "猪肉、盐、糖、白酒",
                "存储条件": "常温保存",
                "保质期": "90天",
                "净含量": "300g",
                "食用方法": "蒸煮后食用",
                "生产加工工艺": "灌制风干",
                "食品分类号": "02.01.01",
                "食品分类名称": "中式香肠",
                "食品原产地": "中国"
            },
            "expected_matches": ["灌肠", "腊肠", "香肠"]
        },
        {
            "name": "液体乳 - 测试与乳制品的匹配",
            "data": {
                "品名": "纯牛奶",
                "原配料": "生牛乳、维生素D",
                "存储条件": "冷藏保存",
                "保质期": "30天",
                "净含量": "1L",
                "食用方法": "直接饮用",
                "生产加工工艺": "超高温灭菌",
                "食品分类号": "01.01.01",
                "食品分类名称": "液体乳",
                "食品原产地": "新西兰"
            },
            "expected_matches": ["牛奶", "液体乳", "乳制品"]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*80}")
        print(f"测试 {i}: {test_case['name']}")
        print(f"{'='*80}")
        
        print(f"📤 食品分类名称: {test_case['data']['食品分类名称']}")
        print(f"📍 原产地: {test_case['data']['食品原产地']}")
        print(f"🎯 预期可能匹配的产品: {test_case['expected_matches']}")
        
        try:
            response = requests.post(
                "http://localhost:8000/check-food-entry/",
                json=test_case['data'],
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"\n🎯 最终结果: {result.get('准入状态')} - {result.get('原因')}")
                
                # 查找详细的产品分析结果
                detailed_analysis_found = False
                if "详细信息" in result and "检查过程" in result["详细信息"]:
                    检查过程 = result["详细信息"]["检查过程"]
                    
                    # 检查乳制品或肉制品的详细分析
                    for 步骤名, 步骤信息 in 检查过程.items():
                        if ("乳制品检查" in 步骤名 or "肉制品检查" in 步骤名) and isinstance(步骤信息, dict):
                            if "详情" in 步骤信息 and "大模型完整分类匹配结果" in 步骤信息["详情"]:
                                detailed_analysis_found = True
                                print(f"\n🤖 {步骤名} - 逐一产品分析:")
                                print_detailed_analysis(步骤信息["详情"]["大模型完整分类匹配结果"])
                
                if not detailed_analysis_found:
                    print(f"\n⚠️ 未找到逐一产品分析结果")
                
                # 评估分析质量
                analysis_quality_score = 0
                
                # 检查是否有详细分析
                result_text = json.dumps(result, ensure_ascii=False)
                if "detailed_analysis" in result_text:
                    analysis_quality_score += 1
                    print(f"\n📊 分析质量评估:")
                    print(f"  包含详细分析: ✅")
                    
                    # 统计分析的产品数量
                    detailed_analysis_count = result_text.count('"allowed_product"')
                    print(f"  分析的产品数量: {detailed_analysis_count}")
                    
                    # 检查是否有匹配分数
                    if "match_score" in result_text:
                        analysis_quality_score += 1
                        print(f"  包含匹配分数: ✅")
                    else:
                        print(f"  包含匹配分数: ❌")
                    
                    # 检查是否有匹配原因
                    if "match_reason" in result_text:
                        analysis_quality_score += 1
                        print(f"  包含匹配原因: ✅")
                    else:
                        print(f"  包含匹配原因: ❌")
                    
                    # 检查是否有最佳匹配
                    if result.get('准入状态') == '准入':
                        analysis_quality_score += 1
                        print(f"  找到最佳匹配: ✅")
                    else:
                        print(f"  找到最佳匹配: ❌")
                else:
                    print(f"\n📊 分析质量评估:")
                    print(f"  包含详细分析: ❌")
                
                # 整体评估
                print(f"  整体分析质量: {'✅ 优秀' if analysis_quality_score >= 3 else '⚠️ 良好' if analysis_quality_score >= 2 else '❌ 需改进'} ({analysis_quality_score}/4)")
                
                # 检查映射信息的使用
                mapping_info_used = "映射记录" in result_text and "说明" in result_text
                print(f"  映射信息使用: {'✅ 是' if mapping_info_used else '❌ 否'}")
                
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(response.text)
                
        except Exception as e:
            print(f"💥 错误: {str(e)}")

def main():
    """主函数"""
    print("🧪 测试逐一产品分析功能")
    
    # 检查服务器
    try:
        response = requests.get("http://localhost:8000/health", timeout=3)
        if response.status_code == 200:
            print("✅ 服务器正常")
        else:
            print("⚠️ 服务器异常")
            return
    except:
        print("❌ 无法连接服务器")
        return
    
    test_detailed_product_analysis()
    
    print(f"\n🎉 测试完成!")
    print(f"\n💡 逐一产品分析功能说明:")
    print(f"  - 大模型现在会对允许产品列表中的每一个产品进行具体分析")
    print(f"  - 每个产品都会得到匹配分数、匹配原因和是否匹配的判断")
    print(f"  - 结合映射记录的说明和主要特征进行语义分析")
    print(f"  - 提供详细的分析过程和最佳匹配结果")
    print(f"  - 即使大模型不可用，备选方案也会进行逐一分析")

if __name__ == "__main__":
    main()
