from sqlalchemy.orm import Session
from typing import List, Optional
from .models import *

# 2760映射表
def get_mapping_tables(db: Session, skip: int = 0, limit: int = 100) -> List[MappingTable]:
    return db.query(MappingTable).offset(skip).limit(limit).all()

def get_mapping_table_by_id(db: Session, id: int) -> Optional[MappingTable]:
    return db.query(MappingTable).filter(MappingTable.id == id).first()

def get_mapping_table_by_category(db: Session, category: str) -> List[MappingTable]:
    return db.query(MappingTable).filter(MappingTable.食品类别.like(f"%{category}%")).all()

# 三新食品原料数据库
def get_new_food_materials(db: Session, skip: int = 0, limit: int = 100) -> List[NewFoodMaterial]:
    return db.query(NewFoodMaterial).offset(skip).limit(limit).all()

def get_new_food_material_by_name(db: Session, name: str) -> List[NewFoodMaterial]:
    return db.query(NewFoodMaterial).filter(NewFoodMaterial.新食品原料名称.like(f"%{name}%")).all()

# 中国食物成分表
def get_food_compositions(db: Session, skip: int = 0, limit: int = 100) -> List[FoodComposition]:
    return db.query(FoodComposition).offset(skip).limit(limit).all()

def get_food_composition_by_name(db: Session, name: str) -> List[FoodComposition]:
    return db.query(FoodComposition).filter(FoodComposition.名称.like(f"%{name}%")).all()

# 乳制品准入名单
def get_dairy_access_lists(db: Session, skip: int = 0, limit: int = 100) -> List[DairyAccessList]:
    return db.query(DairyAccessList).offset(skip).limit(limit).all()

def get_dairy_access_by_country(db: Session, country: str) -> List[DairyAccessList]:
    return db.query(DairyAccessList).filter(DairyAccessList.国家和地区.like(f"%{country}%")).all()

# 加工助剂数据库
def get_processing_aids(db: Session, skip: int = 0, limit: int = 100) -> List[ProcessingAid]:
    return db.query(ProcessingAid).offset(skip).limit(limit).all()

def get_processing_aid_by_name(db: Session, name: str) -> List[ProcessingAid]:
    return db.query(ProcessingAid).filter(ProcessingAid.加工助剂名称.like(f"%{name}%")).all()

# 动植物源性药材准入名单
def get_medicinal_material_accesses(db: Session, skip: int = 0, limit: int = 100) -> List[MedicinalMaterialAccess]:
    return db.query(MedicinalMaterialAccess).offset(skip).limit(limit).all()

def get_medicinal_material_by_name(db: Session, name: str) -> List[MedicinalMaterialAccess]:
    return db.query(MedicinalMaterialAccess).filter(MedicinalMaterialAccess.产品名称.like(f"%{name}%")).all()

# 动物疫情名单
def get_animal_epidemics(db: Session, skip: int = 0, limit: int = 100) -> List[AnimalEpidemic]:
    return db.query(AnimalEpidemic).offset(skip).limit(limit).all()

def get_animal_epidemic_by_country(db: Session, country: str) -> List[AnimalEpidemic]:
    return db.query(AnimalEpidemic).filter(AnimalEpidemic.国家或地区.like(f"%{country}%")).all()

# 可用于婴幼儿食品的菌种数据库
def get_infant_probiotics(db: Session, skip: int = 0, limit: int = 100) -> List[InfantProbiotics]:
    return db.query(InfantProbiotics).offset(skip).limit(limit).all()

def get_infant_probiotic_by_name(db: Session, name: str) -> List[InfantProbiotics]:
    return db.query(InfantProbiotics).filter(InfantProbiotics.可用于婴幼儿食品的菌种名称.like(f"%{name}%")).all()

# 可用于食品的菌种数据库
def get_food_probiotics(db: Session, skip: int = 0, limit: int = 100) -> List[FoodProbiotics]:
    return db.query(FoodProbiotics).offset(skip).limit(limit).all()

def get_food_probiotic_by_name(db: Session, name: str) -> List[FoodProbiotics]:
    return db.query(FoodProbiotics).filter(FoodProbiotics.可用于食品的菌种名称.like(f"%{name}%")).all()

# 咖啡豆和可可豆的允许准入名单
def get_coffee_cocoa_accesses(db: Session, skip: int = 0, limit: int = 100) -> List[CoffeeCocoaAccess]:
    return db.query(CoffeeCocoaAccess).offset(skip).limit(limit).all()

def get_coffee_cocoa_access_by_type(db: Session, type: str) -> List[CoffeeCocoaAccess]:
    return db.query(CoffeeCocoaAccess).filter(CoffeeCocoaAccess.种类.like(f"%{type}%")).all()

# 新鲜水果准入名单
def get_fresh_fruit_accesses(db: Session, skip: int = 0, limit: int = 100) -> List[FreshFruitAccess]:
    return db.query(FreshFruitAccess).offset(skip).limit(limit).all()

def get_fresh_fruit_access_by_country(db: Session, country: str) -> List[FreshFruitAccess]:
    return db.query(FreshFruitAccess).filter(FreshFruitAccess.输出国家_地区.like(f"%{country}%")).all()

# 新鲜蔬菜、香辛料等植物准入名单
def get_vegetable_spice_accesses(db: Session, skip: int = 0, limit: int = 100) -> List[VegetableSpiceAccess]:
    return db.query(VegetableSpiceAccess).offset(skip).limit(limit).all()

def get_vegetable_spice_access_by_name(db: Session, name: str) -> List[VegetableSpiceAccess]:
    return db.query(VegetableSpiceAccess).filter(VegetableSpiceAccess.产品名称.like(f"%{name}%")).all()

# 植物源性准入名单
def get_plant_origin_accesses(db: Session, skip: int = 0, limit: int = 100) -> List[PlantOriginAccess]:
    return db.query(PlantOriginAccess).offset(skip).limit(limit).all()

def get_plant_origin_access_by_name(db: Session, name: str) -> List[PlantOriginAccess]:
    return db.query(PlantOriginAccess).filter(PlantOriginAccess.产品名称.like(f"%{name}%")).all()

# 水产品准入名单
def get_aquatic_product_accesses(db: Session, skip: int = 0, limit: int = 100) -> List[AquaticProductAccess]:
    return db.query(AquaticProductAccess).offset(skip).limit(limit).all()

def get_aquatic_product_access_by_name(db: Session, name: str) -> List[AquaticProductAccess]:
    return db.query(AquaticProductAccess).filter(AquaticProductAccess.产品名称.like(f"%{name}%")).all()

# 添加剂数据库
def get_food_additives(db: Session, skip: int = 0, limit: int = 100) -> List[FoodAdditive]:
    return db.query(FoodAdditive).offset(skip).limit(limit).all()

def get_food_additive_by_name(db: Session, name: str) -> List[FoodAdditive]:
    return db.query(FoodAdditive).filter(FoodAdditive.添加剂名称.like(f"%{name}%")).all()

# 肉制品准入名单
def get_meat_product_accesses(db: Session, skip: int = 0, limit: int = 100) -> List[MeatProductAccess]:
    return db.query(MeatProductAccess).offset(skip).limit(limit).all()

def get_meat_product_access_by_region(db: Session, region: str) -> List[MeatProductAccess]:
    return db.query(MeatProductAccess).filter(MeatProductAccess.地区.like(f"%{region}%")).all()

# 胶基数据库
def get_gum_bases(db: Session, skip: int = 0, limit: int = 100) -> List[GumBase]:
    return db.query(GumBase).offset(skip).limit(limit).all()

def get_gum_base_by_name(db: Session, name: str) -> List[GumBase]:
    return db.query(GumBase).filter(GumBase.胶基配料.like(f"%{name}%")).all()

# 药食同源名单
def get_food_medicine_same_origins(db: Session, skip: int = 0, limit: int = 100) -> List[FoodMedicineSameOrigin]:
    return db.query(FoodMedicineSameOrigin).offset(skip).limit(limit).all()

def get_food_medicine_same_origin_by_name(db: Session, name: str) -> List[FoodMedicineSameOrigin]:
    return db.query(FoodMedicineSameOrigin).filter(FoodMedicineSameOrigin.既是食品又是药品的物品名称.like(f"%{name}%")).all()

# 营养强化剂数据库
def get_nutrient_fortifiers(db: Session, skip: int = 0, limit: int = 100) -> List[NutrientFortifier]:
    return db.query(NutrientFortifier).offset(skip).limit(limit).all()

def get_nutrient_fortifier_by_name(db: Session, name: str) -> List[NutrientFortifier]:
    return db.query(NutrientFortifier).filter(NutrientFortifier.营养强化剂名称.like(f"%{name}%")).all()

# 进口粮食、动物饲料准入名单
def get_grain_feed_accesses(db: Session, skip: int = 0, limit: int = 100) -> List[GrainFeedAccess]:
    return db.query(GrainFeedAccess).offset(skip).limit(limit).all()

def get_grain_feed_access_by_type(db: Session, type: str) -> List[GrainFeedAccess]:
    return db.query(GrainFeedAccess).filter(GrainFeedAccess.类型.like(f"%{type}%")).all()

# 酶制剂数据库
def get_enzyme_preparations(db: Session, skip: int = 0, limit: int = 100) -> List[EnzymePreparation]:
    return db.query(EnzymePreparation).offset(skip).limit(limit).all()

def get_enzyme_preparation_by_name(db: Session, name: str) -> List[EnzymePreparation]:
    return db.query(EnzymePreparation).filter(EnzymePreparation.酶制剂名称.like(f"%{name}%")).all()

# 香精香料数据库
def get_flavor_spices(db: Session, skip: int = 0, limit: int = 100) -> List[FlavorSpice]:
    return db.query(FlavorSpice).offset(skip).limit(limit).all()

def get_flavor_spice_by_name(db: Session, name: str) -> List[FlavorSpice]:
    return db.query(FlavorSpice).filter(FlavorSpice.香料中文名称.like(f"%{name}%")).all()













# 关键查询函数

def get_mapping_by_classification(db: Session, 分类号: str):
    """根据分类号获取映射信息"""
    return db.query(MappingTable).filter(MappingTable.分类号 == 分类号).first()

def get_animal_epidemic_by_country(db: Session, country: str) -> List:
    """根据国家获取动物疫情信息"""
    return db.query(AnimalEpidemic).filter(AnimalEpidemic.国家或地区.like(f"%{country}%")).all()

def get_dairy_access_by_country(db: Session, country: str) -> List:
    """根据国家获取乳制品准入信息"""
    return db.query(DairyAccessList).filter(DairyAccessList.国家和地区.like(f"%{country}%")).all()

def get_meat_product_access_by_country(db: Session, country: str) -> List:
    """根据国家获取肉制品准入信息"""
    return db.query(MeatProductAccess).filter(MeatProductAccess.地区.like(f"%{country}%")).all()

def get_plant_origin_access_by_country(db: Session, country: str) -> List:
    """根据国家获取植物源性准入信息"""
    return db.query(PlantOriginAccess).filter(PlantOriginAccess.国家和地区.like(f"%{country}%")).all()

# 其他准入名单查询函数
def get_coffee_cocoa_access_by_country(db: Session, country: str) -> List:
    try:
        return db.query(CoffeeCocoaAccess).filter(CoffeeCocoaAccess.国家和地区.like(f"%{country}%")).all()
    except:
        return []

def get_aquatic_product_access_by_country(db: Session, country: str) -> List:
    try:
        return db.query(AquaticProductAccess).filter(AquaticProductAccess.国家和地区.like(f"%{country}%")).all()
    except:
        return []

def get_medicinal_material_access_by_country(db: Session, country: str) -> List:
    try:
        return db.query(MedicinalMaterialAccess).filter(MedicinalMaterialAccess.国家或地区.like(f"%{country}%")).all()
    except:
        return []

def get_fresh_fruit_access_by_country(db: Session, country: str) -> List:
    try:
        return db.query(FreshFruitAccess).filter(FreshFruitAccess.输出国家_地区.like(f"%{country}%")).all()
    except:
        return []

def get_vegetable_spice_access_by_country(db: Session, country: str) -> List:
    try:
        return db.query(VegetableSpiceAccess).filter(VegetableSpiceAccess.国家和地区.like(f"%{country}%")).all()
    except:
        return []

def get_grain_feed_access_by_country(db: Session, country: str) -> List:
    try:
        return db.query(GrainFeedAccess).filter(GrainFeedAccess.输出国家或地区.like(f"%{country}%")).all()
    except:
        return []
