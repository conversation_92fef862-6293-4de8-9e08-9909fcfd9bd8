#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新增的3个数据库表的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal
from app.models import CasingAccess, BeeProductAccess, BirdNestAccess
from app.crud import get_casing_access_by_country, get_bee_product_access_by_country, get_bird_nest_access_by_country

def test_new_tables():
    """测试新增的3个表"""
    db = SessionLocal()
    
    try:
        print("=== 测试新增的3个数据库表 ===\n")
        
        # 测试肠衣准入名单
        print("1. 测试肠衣准入名单表")
        print("   表名: 肠衣准入名单")
        print("   字段: id, 国家或地区, 产品名称, 准入状态")
        
        # 尝试查询（即使没有数据也能验证表结构）
        casing_records = get_casing_access_by_country(db, "中国")
        print(f"   查询结果数量: {len(casing_records)}")
        
        # 测试蜂产品准入名单
        print("\n2. 测试蜂产品准入名单表")
        print("   表名: 蜂产品准入名单")
        print("   字段: id, 国家或地区, 产品名称, 状态")
        
        bee_records = get_bee_product_access_by_country(db, "中国")
        print(f"   查询结果数量: {len(bee_records)}")
        
        # 测试燕窝准入名单
        print("\n3. 测试燕窝准入名单表")
        print("   表名: 燕窝准入名单")
        print("   字段: id, 国家或地区, 产品名称, 准入状态")
        
        bird_nest_records = get_bird_nest_access_by_country(db, "中国")
        print(f"   查询结果数量: {len(bird_nest_records)}")
        
        print("\n=== 表结构验证 ===")
        
        # 验证模型属性
        print("\n肠衣准入名单模型属性:")
        casing_attrs = [attr for attr in dir(CasingAccess) if not attr.startswith('_')]
        for attr in casing_attrs:
            if hasattr(getattr(CasingAccess, attr), 'type'):
                print(f"   {attr}: {getattr(CasingAccess, attr).type}")
        
        print("\n蜂产品准入名单模型属性:")
        bee_attrs = [attr for attr in dir(BeeProductAccess) if not attr.startswith('_')]
        for attr in bee_attrs:
            if hasattr(getattr(BeeProductAccess, attr), 'type'):
                print(f"   {attr}: {getattr(BeeProductAccess, attr).type}")
        
        print("\n燕窝准入名单模型属性:")
        bird_attrs = [attr for attr in dir(BirdNestAccess) if not attr.startswith('_')]
        for attr in bird_attrs:
            if hasattr(getattr(BirdNestAccess, attr), 'type'):
                print(f"   {attr}: {getattr(BirdNestAccess, attr).type}")
        
        print("\n✅ 所有新表都已成功创建并可以正常查询！")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    test_new_tables()
