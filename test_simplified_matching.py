#!/usr/bin/env python3
"""
测试简化后的匹配逻辑
"""

import requests
import json

def test_simplified_matching():
    """测试简化后的匹配逻辑"""
    
    # 使用之前失败的西式火腿案例
    test_data = {
        "品名": "西式火腿",
        "原配料": "猪肉、盐、糖、香辛料",
        "存储条件": "冷藏保存",
        "保质期": "60天",
        "净含量": "500g",
        "食用方法": "切片食用",
        "生产加工工艺": "熏烤制作",
        "食品分类号": "08.03.04",
        "食品分类名称": "西式火腿(熏烤、烟熏、蒸煮火腿)类",
        "食品原产地": "德国"
    }
    
    print("🧪 测试简化后的匹配逻辑")
    print(f"📤 食品分类名称: {test_data['食品分类名称']}")
    print(f"📍 原产地: {test_data['食品原产地']}")
    print(f"🎯 预期: 应该匹配到熟制火腿相关产品")
    
    try:
        response = requests.post(
            "http://localhost:8000/check-food-entry/",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"\n🎯 最终结果: {result.get('准入状态')} - {result.get('原因')}")
            
            # 查找映射信息
            if "详细信息" in result and "映射查找结果" in result["详细信息"]:
                映射查找结果 = result["详细信息"]["映射查找结果"]
                映射记录 = 映射查找结果.get('映射记录')
                
                if 映射记录:
                    print(f"\n🗺️ 映射记录信息:")
                    print(f"  食品类别: {映射记录.get('食品类别', 'N/A')}")
                    print(f"  说明: {映射记录.get('说明', 'N/A')}")
                    print(f"  主要特征: {映射记录.get('主要特征', 'N/A')}")
            
            # 查找大模型分析结果
            if "详细信息" in result and "检查过程" in result["详细信息"]:
                检查过程 = result["详细信息"]["检查过程"]
                
                for 步骤名, 步骤信息 in 检查过程.items():
                    if "肉制品检查" in 步骤名 and isinstance(步骤信息, dict):
                        if "详情" in 步骤信息:
                            详情 = 步骤信息["详情"]
                            
                            # 显示允许产品列表的部分内容
                            if "允许的产品列表" in 详情:
                                允许产品 = 详情["允许的产品列表"]
                                print(f"\n📋 允许产品列表 (总数: {len(允许产品)}):")
                                
                                # 查找火腿相关产品
                                火腿相关 = [p for p in 允许产品 if '火腿' in str(p)]
                                熟制相关 = [p for p in 允许产品 if '熟制' in str(p)]
                                
                                print(f"  火腿相关产品: {len(火腿相关)} 个")
                                for i, product in enumerate(火腿相关[:3], 1):
                                    print(f"    [{i}] {product}")
                                
                                print(f"  熟制相关产品: {len(熟制相关)} 个")
                                for i, product in enumerate(熟制相关[:3], 1):
                                    print(f"    [{i}] {product}")
                            
                            # 显示大模型分析结果
                            if "大模型完整分类匹配结果" in 详情:
                                大模型结果 = 详情["大模型完整分类匹配结果"]
                                print(f"\n🤖 大模型分析结果:")
                                print(f"  是否允许: {大模型结果.get('is_allowed', 'N/A')}")
                                print(f"  匹配产品: {大模型结果.get('matched_product', 'N/A')}")
                                print(f"  置信度: {大模型结果.get('confidence', 'N/A')}")
                                print(f"  分析理由: {大模型结果.get('reason', 'N/A')}")
                                
                                # 显示详细分析的前几项
                                detailed_analysis = 大模型结果.get('detailed_analysis', [])
                                if detailed_analysis:
                                    print(f"\n📊 详细分析 (前5项):")
                                    for i, item in enumerate(detailed_analysis[:5], 1):
                                        match_status = "✅" if item.get('is_match') else "❌"
                                        print(f"  [{i}] {match_status} {item.get('allowed_product', 'N/A')}")
                                        print(f"      分数: {item.get('match_score', 'N/A')}")
                                        print(f"      理由: {item.get('match_reason', 'N/A')}")
                                
                                # 评估匹配效果
                                is_allowed = 大模型结果.get('is_allowed', False)
                                matched_product = 大模型结果.get('matched_product', '')
                                
                                print(f"\n📈 匹配效果评估:")
                                print(f"  成功匹配: {'✅ 是' if is_allowed else '❌ 否'}")
                                if matched_product:
                                    print(f"  匹配产品: {matched_product}")
                                    if '火腿' in matched_product or '熟制' in matched_product:
                                        print(f"  匹配合理性: ✅ 合理（包含火腿或熟制关键词）")
                                    else:
                                        print(f"  匹配合理性: ⚠️ 需检查")
                                else:
                                    print(f"  匹配产品: 无")
                                
                                # 检查是否找到了明显应该匹配的产品
                                should_match_products = [
                                    "熟制火腿、熟制香肠、肉酱类产品（熟肉酱、肉冻、肉丸）",
                                    "熟制猪肉火腿、熟制猪肉香肠、熟制猪肉块",
                                    "干火腿"
                                ]
                                
                                found_obvious_matches = []
                                for product in should_match_products:
                                    if product in 允许产品:
                                        found_obvious_matches.append(product)
                                
                                print(f"  明显应匹配产品: {len(found_obvious_matches)} 个")
                                for product in found_obvious_matches:
                                    print(f"    - {product}")
                                
                                if found_obvious_matches and not is_allowed:
                                    print(f"  ⚠️ 存在明显应匹配的产品但未成功匹配")
                                elif found_obvious_matches and is_allowed:
                                    print(f"  ✅ 成功识别并匹配了相关产品")
            
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"💥 错误: {str(e)}")

def main():
    """主函数"""
    # 检查服务器
    try:
        response = requests.get("http://localhost:8000/health", timeout=3)
        if response.status_code == 200:
            print("✅ 服务器正常")
        else:
            print("⚠️ 服务器异常")
            return
    except:
        print("❌ 无法连接服务器")
        return
    
    test_simplified_matching()
    
    print(f"\n🎉 测试完成!")
    print(f"\n💡 简化提示词说明:")
    print(f"  - 简化了提示词长度，保持适中")
    print(f"  - 重点突出核心匹配逻辑")
    print(f"  - 强调基于说明和特征进行判断")
    print(f"  - 避免过度复杂的指导说明")

if __name__ == "__main__":
    main()
