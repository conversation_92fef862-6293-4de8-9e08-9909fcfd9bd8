#!/usr/bin/env python3
"""
测试允许产品列表是否已经扁平化
"""

import requests
import json

def analyze_product_list_structure(product_list):
    """分析产品列表的结构"""
    if not isinstance(product_list, list):
        return {
            "is_list": False,
            "type": type(product_list).__name__,
            "structure": "非列表类型"
        }
    
    nested_count = 0
    string_count = 0
    other_count = 0
    
    for item in product_list:
        if isinstance(item, list):
            nested_count += 1
        elif isinstance(item, str):
            string_count += 1
        else:
            other_count += 1
    
    return {
        "is_list": True,
        "total_items": len(product_list),
        "string_items": string_count,
        "nested_items": nested_count,
        "other_items": other_count,
        "is_flattened": nested_count == 0,
        "structure": "扁平化" if nested_count == 0 else f"嵌套（{nested_count}个嵌套项）"
    }

def test_flattened_products():
    """测试允许产品列表扁平化"""
    
    test_cases = [
        {
            "name": "肉制品检查 - 测试品名字段扁平化",
            "data": {
                "品名": "火腿",
                "原配料": "猪肉、盐、糖、香辛料",
                "存储条件": "冷藏保存",
                "保质期": "60天",
                "净含量": "500g",
                "食用方法": "切片食用",
                "生产加工工艺": "熏制",
                "食品分类号": "02.01.01",
                "食品分类名称": "肉制品",
                "食品原产地": "德国"
            },
            "expected_category": "肉制品检查"
        },
        {
            "name": "乳制品检查 - 测试产品名称扁平化",
            "data": {
                "品名": "纯牛奶",
                "原配料": "生牛乳、维生素D",
                "存储条件": "冷藏保存",
                "保质期": "30天",
                "净含量": "1L",
                "食用方法": "直接饮用",
                "生产加工工艺": "超高温灭菌",
                "食品分类号": "01.01.01",
                "食品分类名称": "液体乳",
                "食品原产地": "新西兰"
            },
            "expected_category": "乳制品检查"
        },
        {
            "name": "未经加工食品检查 - 测试多字段扁平化",
            "data": {
                "品名": "苹果",
                "原配料": "苹果",
                "存储条件": "冷藏保存",
                "保质期": "30天",
                "净含量": "1kg",
                "食用方法": "直接食用",
                "生产加工工艺": "清洗包装",
                "食品分类号": "03.01.01",
                "食品分类名称": "新鲜水果",
                "食品原产地": "美国"
            },
            "expected_category": "未经加工食品检查"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*80}")
        print(f"测试 {i}: {test_case['name']}")
        print(f"{'='*80}")
        
        print(f"📤 食品分类名称: {test_case['data']['食品分类名称']}")
        print(f"📍 原产地: {test_case['data']['食品原产地']}")
        print(f"🎯 预期检查类别: {test_case['expected_category']}")
        
        try:
            response = requests.post(
                "http://localhost:8000/check-food-entry/",
                json=test_case['data'],
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"\n🎯 最终结果: {result.get('准入状态')} - {result.get('原因')}")
                
                # 查找允许产品列表
                product_lists_found = []
                if "详细信息" in result and "检查过程" in result["详细信息"]:
                    检查过程 = result["详细信息"]["检查过程"]
                    
                    for 步骤名, 步骤信息 in 检查过程.items():
                        if isinstance(步骤信息, dict) and "详情" in 步骤信息:
                            详情 = 步骤信息["详情"]
                            
                            if "允许的产品列表" in 详情:
                                product_list = 详情["允许的产品列表"]
                                analysis = analyze_product_list_structure(product_list)
                                
                                product_lists_found.append({
                                    "step_name": 步骤名,
                                    "product_list": product_list,
                                    "analysis": analysis
                                })
                                
                                print(f"\n📋 {步骤名} - 允许产品列表分析:")
                                print(f"  列表类型: {'✅ 列表' if analysis['is_list'] else '❌ ' + analysis['type']}")
                                if analysis['is_list']:
                                    print(f"  总项目数: {analysis['total_items']}")
                                    print(f"  字符串项: {analysis['string_items']}")
                                    print(f"  嵌套项: {analysis['nested_items']}")
                                    print(f"  其他类型项: {analysis['other_items']}")
                                    print(f"  结构状态: {'✅ ' + analysis['structure'] if analysis['is_flattened'] else '❌ ' + analysis['structure']}")
                                    
                                    # 显示前几个产品示例
                                    if analysis['total_items'] > 0:
                                        print(f"  产品示例 (前5个):")
                                        for j, product in enumerate(product_list[:5], 1):
                                            product_type = type(product).__name__
                                            if isinstance(product, list):
                                                print(f"    [{j}] {product_type}: {product} ❌ 嵌套列表")
                                            elif isinstance(product, str):
                                                print(f"    [{j}] {product_type}: '{product}' ✅")
                                            else:
                                                print(f"    [{j}] {product_type}: {product} ⚠️ 非字符串")
                
                if not product_lists_found:
                    print(f"\n⚠️ 未找到允许产品列表")
                
                # 整体评估
                all_flattened = all(item["analysis"]["is_flattened"] for item in product_lists_found)
                total_nested = sum(item["analysis"]["nested_items"] for item in product_lists_found)
                
                print(f"\n📊 扁平化状态评估:")
                print(f"  找到的产品列表数: {len(product_lists_found)}")
                print(f"  全部扁平化: {'✅ 是' if all_flattened else '❌ 否'}")
                print(f"  总嵌套项数: {total_nested}")
                print(f"  修复状态: {'✅ 成功' if total_nested == 0 else '❌ 仍有嵌套'}")
                
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(response.text)
                
        except Exception as e:
            print(f"💥 错误: {str(e)}")

def main():
    """主函数"""
    print("🧪 测试允许产品列表扁平化")
    
    # 检查服务器
    try:
        response = requests.get("http://localhost:8000/health", timeout=3)
        if response.status_code == 200:
            print("✅ 服务器正常")
        else:
            print("⚠️ 服务器异常")
            return
    except:
        print("❌ 无法连接服务器")
        return
    
    test_flattened_products()
    
    print(f"\n🎉 测试完成!")
    print(f"\n💡 扁平化修复说明:")
    print(f"  - 修复了肉制品检查中品名字段的嵌套列表问题")
    print(f"  - 修复了未经加工食品检查中多字段的嵌套列表问题")
    print(f"  - 确保所有允许产品列表都是扁平的字符串列表")
    print(f"  - 添加了去重和空值过滤逻辑")
    print(f"  - 现在大模型可以正确处理允许产品列表")

if __name__ == "__main__":
    main()
