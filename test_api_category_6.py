#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的API流程，包括映射类别号6
"""

import sys
import os
import asyncio
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal
from app.schemas import FoodCheckRequest
from app.main import check_food_import

async def test_full_api_flow():
    """测试完整的API流程"""
    db = SessionLocal()
    
    try:
        print("=== 测试完整API流程（包括映射类别号6） ===\n")
        
        # 测试用例：映射类别号6的产品
        test_cases = [
            {
                "name": "咖啡豆制品（映射类别号6）",
                "request": FoodCheckRequest(
                    食品原产地="美国",
                    食品分类名称="咖啡豆制品",
                    品名="烘焙咖啡豆",
                    食品配料="咖啡豆, 天然香料",
                    存储条件="常温"
                ),
                "expected_category": 6
            },
            {
                "name": "蜂蜜制品（映射类别号6）",
                "request": FoodCheckRequest(
                    食品原产地="新西兰",
                    食品分类名称="蜂蜜制品",
                    品名="麦卢卡蜂蜜",
                    食品配料="蜂蜜",
                    存储条件="常温"
                ),
                "expected_category": 6
            },
            {
                "name": "植物提取物（映射类别号6）",
                "request": FoodCheckRequest(
                    食品原产地="德国",
                    食品分类名称="植物提取物",
                    品名="草本提取物",
                    食品配料="植物提取物, 载体",
                    存储条件="阴凉干燥"
                ),
                "expected_category": 6
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"{i}. 测试 {test_case['name']}")
            print(f"   原产地: {test_case['request'].食品原产地}")
            print(f"   分类名称: {test_case['request'].食品分类名称}")
            print(f"   配料: {test_case['request'].食品配料}")
            
            try:
                result = await check_food_import(db, test_case['request'])
                
                print(f"   结果: {'准入' if result['allowed'] else '不准入'}")
                print(f"   原因: {result['reason']}")
                
                # 检查映射类别号
                details = result.get('details', {})
                mapping_category = details.get('映射类别号')
                if mapping_category == test_case['expected_category']:
                    print(f"   ✅ 映射类别号正确: {mapping_category}")
                else:
                    print(f"   ⚠️  映射类别号: {mapping_category} (期望: {test_case['expected_category']})")
                
                # 显示检查类型
                check_type = details.get('检查类型', '未知')
                print(f"   检查类型: {check_type}")
                
                # 如果是未深加工产品，显示更多详情
                if check_type == "未深加工产品":
                    print(f"   准入记录数: {details.get('准入记录数', 0)}")
                    print(f"   允许产品数: {details.get('允许的产品数', 0)}")
                    related_ingredients = details.get('提取的相关配料', [])
                    if related_ingredients:
                        print(f"   提取的相关配料: {related_ingredients}")
                
            except Exception as e:
                print(f"   ❌ 测试失败: {str(e)}")
            
            print()
        
        print("=== 测试总结 ===")
        print("✅ 映射类别号6（未深加工产品）的完整流程已实现：")
        print("   1. 根据食品分类名称映射到类别号6")
        print("   2. 查询8个相关准入名单表")
        print("   3. 提取允许的产品名称列表")
        print("   4. 调用大模型提取与分类相关的配料")
        print("   5. 判断配料是否在允许产品列表中")
        print("   6. 返回详细的检查结果和原因")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_full_api_flow())
